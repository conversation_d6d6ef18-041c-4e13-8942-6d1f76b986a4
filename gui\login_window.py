import tkinter as tk
from tkinter import ttk, messagebox
from database import Database
from utils import (
    center_window, Colors, Fonts, Spacing,
    create_modern_button, create_modern_entry, create_card_frame,
    create_icon_label, apply_modern_style
)

class LoginWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Système de Gestion de Stock - Connexion")
        self.root.configure(bg=Colors.BACKGROUND)
        self.root.resizable(False, False)

        # Centrer la fenêtre avec une taille plus grande
        center_window(self.root, 500, 600)

        self.db = Database()
        self.user = None

        self.create_widgets()

    def create_widgets(self):
        # Arrière-plan avec dégradé simulé
        self.create_background()

        # Conteneur principal centré
        main_container = tk.Frame(self.root, bg=Colors.BACKGROUND)
        main_container.pack(expand=True, fill=tk.BOTH)

        # Carte de connexion (sans ombre pour éviter les erreurs)
        login_card = create_card_frame(main_container, Colors.SURFACE, shadow=False)
        login_card.pack(expand=True, padx=Spacing.XXL, pady=Spacing.XXL)

        # Contenu de la carte
        card_content = tk.Frame(login_card, bg=Colors.SURFACE)
        card_content.pack(expand=True, fill=tk.BOTH, padx=Spacing.XL, pady=Spacing.XL)

        # En-tête avec logo et titre
        self.create_header(card_content)

        # Formulaire de connexion
        self.create_login_form(card_content)

        # Informations et pied de page
        self.create_footer(card_content)

        # Bind Enter key
        self.root.bind('<Return>', lambda event: self.login())

        # Focus sur le champ username
        self.username_entry.focus()

    def create_background(self):
        """Crée un arrière-plan avec effet de dégradé"""
        # Frame de fond avec couleur primaire
        bg_frame = tk.Frame(self.root, bg=Colors.PRIMARY)
        bg_frame.place(x=0, y=0, relwidth=1, relheight=0.4)

        # Frame de fond avec couleur claire
        bg_frame2 = tk.Frame(self.root, bg=Colors.BACKGROUND)
        bg_frame2.place(x=0, rely=0.4, relwidth=1, relheight=0.6)

    def create_header(self, parent):
        """Crée l'en-tête avec logo et titre"""
        header_frame = tk.Frame(parent, bg=Colors.SURFACE)
        header_frame.pack(fill=tk.X, pady=(0, Spacing.XL))

        # Logo/Icône
        logo_frame = tk.Frame(header_frame, bg=Colors.PRIMARY, width=80, height=80)
        logo_frame.pack_propagate(False)
        logo_frame.pack(pady=(0, Spacing.MD))

        logo_label = tk.Label(
            logo_frame,
            text="📦",
            font=(Fonts.PRIMARY_FONT, 32),
            bg=Colors.PRIMARY,
            fg=Colors.WHITE
        )
        logo_label.pack(expand=True)

        # Titre principal
        title_label = tk.Label(
            header_frame,
            text="SYSTÈME DE GESTION",
            font=(Fonts.PRIMARY_FONT, Fonts.TITLE_SIZE, "bold"),
            bg=Colors.SURFACE,
            fg=Colors.TEXT_PRIMARY
        )
        title_label.pack()

        # Sous-titre
        subtitle_label = tk.Label(
            header_frame,
            text="DE STOCK",
            font=(Fonts.PRIMARY_FONT, Fonts.SUBTITLE_SIZE, "bold"),
            bg=Colors.SURFACE,
            fg=Colors.PRIMARY
        )
        subtitle_label.pack()

        # Description
        desc_label = tk.Label(
            header_frame,
            text="Connectez-vous pour accéder à votre espace",
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE),
            bg=Colors.SURFACE,
            fg=Colors.TEXT_SECONDARY
        )
        desc_label.pack(pady=(Spacing.SM, 0))

    def create_login_form(self, parent):
        """Crée le formulaire de connexion"""
        form_frame = tk.Frame(parent, bg=Colors.SURFACE)
        form_frame.pack(fill=tk.X, pady=Spacing.LG)

        # Champ nom d'utilisateur
        username_label = create_icon_label(
            form_frame,
            "Nom d'utilisateur",
            "👤",
            Colors.PRIMARY,
            Colors.TEXT_PRIMARY
        )
        username_label.pack(anchor=tk.W, pady=(0, Spacing.XS))

        self.username_frame, self.username_entry = create_modern_entry(
            form_frame,
            "Saisissez votre nom d'utilisateur"
        )
        self.username_frame.pack(fill=tk.X, pady=(0, Spacing.MD))

        # Champ mot de passe
        password_label = create_icon_label(
            form_frame,
            "Mot de passe",
            "🔒",
            Colors.PRIMARY,
            Colors.TEXT_PRIMARY
        )
        password_label.pack(anchor=tk.W, pady=(0, Spacing.XS))

        self.password_frame, self.password_entry = create_modern_entry(
            form_frame,
            "Saisissez votre mot de passe"
        )
        self.password_entry.configure(show="*")
        self.password_frame.pack(fill=tk.X, pady=(0, Spacing.LG))

        # Bouton de connexion
        login_btn = create_modern_button(
            form_frame,
            "🔐 SE CONNECTER",
            self.login,
            "primary",
            "large"
        )
        login_btn.pack(fill=tk.X, pady=Spacing.MD)

        # Ligne de séparation
        separator = tk.Frame(form_frame, bg=Colors.BORDER, height=1)
        separator.pack(fill=tk.X, pady=Spacing.LG)

    def create_footer(self, parent):
        """Crée le pied de page avec informations"""
        footer_frame = tk.Frame(parent, bg=Colors.SURFACE)
        footer_frame.pack(fill=tk.X)

        # Carte d'information
        info_card = tk.Frame(
            footer_frame,
            bg=Colors.INFO_LIGHT,
            relief=tk.FLAT,
            bd=1,
            highlightbackground=Colors.INFO,
            highlightthickness=1
        )
        info_card.pack(fill=tk.X, pady=Spacing.MD)

        info_content = tk.Frame(info_card, bg=Colors.INFO_LIGHT)
        info_content.pack(fill=tk.X, padx=Spacing.MD, pady=Spacing.MD)

        # Titre de l'info
        info_title = tk.Label(
            info_content,
            text="ℹ️ Compte par défaut",
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE, "bold"),
            bg=Colors.INFO_LIGHT,
            fg=Colors.INFO_DARK
        )
        info_title.pack(anchor=tk.W)

        # Détails du compte
        account_details = [
            "👤 Utilisateur: admin",
            "🔑 Mot de passe: admin123",
            "🛡️ Rôle: Administrateur"
        ]

        for detail in account_details:
            detail_label = tk.Label(
                info_content,
                text=detail,
                font=(Fonts.PRIMARY_FONT, Fonts.SMALL_SIZE),
                bg=Colors.INFO_LIGHT,
                fg=Colors.INFO_DARK
            )
            detail_label.pack(anchor=tk.W, pady=(2, 0))

        # Version et copyright
        version_frame = tk.Frame(footer_frame, bg=Colors.SURFACE)
        version_frame.pack(fill=tk.X, pady=(Spacing.LG, 0))

        version_label = tk.Label(
            version_frame,
            text="Version 1.0.0 • Développé avec ❤️ en Python",
            font=(Fonts.PRIMARY_FONT, Fonts.CAPTION_SIZE),
            bg=Colors.SURFACE,
            fg=Colors.TEXT_LIGHT
        )
        version_label.pack()

    def login(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        # Vérifier si les champs contiennent les placeholders
        if username == "Saisissez votre nom d'utilisateur" or not username:
            messagebox.showerror("❌ Erreur", "Veuillez saisir votre nom d'utilisateur")
            self.username_entry.focus()
            return

        if password == "Saisissez votre mot de passe" or not password:
            messagebox.showerror("❌ Erreur", "Veuillez saisir votre mot de passe")
            self.password_entry.focus()
            return

        user = self.db.verify_user(username, password)

        if user:
            self.user = user
            # Animation de fermeture (simulée)
            self.animate_close()
        else:
            messagebox.showerror(
                "❌ Erreur de Connexion",
                "Nom d'utilisateur ou mot de passe incorrect.\n\n"
                "Conseil: Utilisez le compte par défaut:\n"
                "Utilisateur: admin\n"
                "Mot de passe: admin123"
            )
            # Effacer le mot de passe et remettre le focus
            self.password_entry.delete(0, tk.END)
            self.password_entry.insert(0, "Saisissez votre mot de passe")
            self.password_entry.configure(fg=Colors.TEXT_LIGHT)
            self.username_entry.focus()

    def animate_close(self):
        """Animation simple de fermeture"""
        # Effet de fondu simulé en réduisant la taille
        for i in range(10, 0, -1):
            self.root.attributes('-alpha', i/10.0)
            self.root.update()
            self.root.after(30)
        self.root.destroy()

    def show(self):
        self.root.mainloop()
        return self.user
