from dataclasses import dataclass
from datetime import datetime
from typing import Optional

@dataclass
class User:
    id: int
    username: str
    role: str

@dataclass
class Product:
    id: int
    code: str
    nom: str
    categorie: str
    unite: str
    stock_actuel: float = 0.0

@dataclass
class Entry:
    id: int
    produit_id: int
    date_entree: str
    quantite: float
    source: Optional[str] = None
    note: Optional[str] = None
    user_id: Optional[int] = None

@dataclass
class Exit:
    id: int
    produit_id: int
    date_sortie: str
    quantite: float
    destination: Optional[str] = None
    motif: Optional[str] = None
    user_id: Optional[int] = None
