# 📋 Documentation Technique - Système de Gestion de Stock

## 🏗️ Architecture de l'application

### Structure modulaire
L'application suit une architecture MVC (Model-View-Controller) adaptée :

- **Models** (`models.py`) : Classes de données
- **Views** (`gui/`) : Interface utilisateur
- **Controller** (`database.py`) : Logique métier et accès aux données
- **Utils** (`utils.py`) : Fonctions utilitaires

### Diagramme de l'architecture
```
main.py
├── LoginWindow (gui/login_window.py)
├── MainWindow (gui/main_window.py)
│   ├── ProductManager (gui/product_manager.py)
│   ├── EntryManager (gui/entry_manager.py)
│   ├── ExitManager (gui/exit_manager.py)
│   └── ReportsManager (gui/reports.py)
├── Database (database.py)
├── Models (models.py)
└── Utils (utils.py)
```

## 🗄️ Base de données

### Schéma SQLite

#### Table `users`
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,  -- SHA-256 hash
    role TEXT NOT NULL DEFAULT 'user'
);
```

#### Table `produits`
```sql
CREATE TABLE produits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    nom TEXT NOT NULL,
    categorie TEXT,
    unite TEXT,
    stock_actuel REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

#### Table `entrees`
```sql
CREATE TABLE entrees (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    produit_id INTEGER NOT NULL,
    date_entree TEXT NOT NULL,
    quantite REAL NOT NULL,
    source TEXT,
    note TEXT,
    user_id INTEGER,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (produit_id) REFERENCES produits(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### Table `sorties`
```sql
CREATE TABLE sorties (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    produit_id INTEGER NOT NULL,
    date_sortie TEXT NOT NULL,
    quantite REAL NOT NULL,
    destination TEXT,
    motif TEXT,
    user_id INTEGER,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (produit_id) REFERENCES produits(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### Relations
- `entrees.produit_id` → `produits.id`
- `sorties.produit_id` → `produits.id`
- `entrees.user_id` → `users.id`
- `sorties.user_id` → `users.id`

## 🔐 Sécurité

### Authentification
- Mots de passe hashés avec SHA-256
- Vérification des rôles pour l'accès aux fonctionnalités
- Session utilisateur maintenue pendant l'exécution

### Contrôle d'accès
```python
# Rôles disponibles
ADMIN = "admin"  # Accès complet
USER = "user"    # Accès limité (pas de gestion produits)
```

### Validation des données
- Validation des formats de date (YYYY-MM-DD)
- Validation des nombres (positifs uniquement)
- Vérification de l'intégrité des stocks

## 🎨 Interface utilisateur

### Composants principaux

#### LoginWindow
- Authentification utilisateur
- Validation des identifiants
- Redirection vers MainWindow

#### MainWindow
- Barre supérieure avec informations utilisateur
- Menu latéral de navigation
- Zone de contenu dynamique

#### Gestionnaires de modules
- **ProductManager** : CRUD des produits
- **EntryManager** : Gestion des entrées
- **ExitManager** : Gestion des sorties
- **ReportsManager** : Génération de rapports

### Palette de couleurs
```python
PRIMARY = "#2E86AB"    # Bleu principal
SECONDARY = "#A23B72"  # Violet secondaire
SUCCESS = "#F18F01"    # Orange succès
DANGER = "#C73E1D"     # Rouge danger
LIGHT = "#F5F5F5"      # Gris clair
DARK = "#333333"       # Gris foncé
WHITE = "#FFFFFF"      # Blanc
GRAY = "#6C757D"       # Gris moyen
```

## 📊 Gestion des données

### Flux de données

#### Ajout d'une entrée
1. Validation des données saisies
2. Insertion dans la table `entrees`
3. Mise à jour du `stock_actuel` dans `produits`
4. Actualisation de l'interface

#### Ajout d'une sortie
1. Validation des données saisies
2. Vérification du stock disponible
3. Insertion dans la table `sorties`
4. Mise à jour du `stock_actuel` dans `produits`
5. Actualisation de l'interface

### Calcul des stocks
```python
# Stock actuel = Entrées totales - Sorties totales
stock_actuel = SUM(entrees.quantite) - SUM(sorties.quantite)
```

## 🔧 API interne

### Classe Database

#### Méthodes d'authentification
```python
verify_user(username, password) -> dict|None
add_user(username, password, role) -> bool
hash_password(password) -> str
```

#### Méthodes de gestion des produits
```python
add_product(code, nom, categorie, unite) -> int|None
get_products() -> list
get_product_by_id(product_id) -> tuple|None
update_product(product_id, code, nom, categorie, unite) -> bool
delete_product(product_id) -> None
```

#### Méthodes de gestion des mouvements
```python
add_entry(produit_id, date_entree, quantite, source, note, user_id) -> bool
add_exit(produit_id, date_sortie, quantite, destination, motif, user_id) -> tuple
get_entries(start_date=None, end_date=None) -> list
get_exits(start_date=None, end_date=None) -> list
```

#### Méthodes de rapports
```python
get_monthly_report(year, month) -> tuple
```

## 🚀 Performance

### Optimisations implémentées
- Index automatiques sur les clés primaires
- Requêtes SQL optimisées avec JOIN
- Chargement paresseux des données
- Mise en cache des listes de produits

### Limitations actuelles
- Pas de pagination pour les grandes listes
- Pas de mise en cache des rapports
- Interface bloquante pendant les opérations

## 🧪 Tests

### Tests manuels recommandés
1. **Authentification**
   - Connexion avec identifiants valides/invalides
   - Test des rôles admin/user

2. **Gestion des produits**
   - Ajout, modification, suppression
   - Validation des codes uniques
   - Recherche et filtrage

3. **Mouvements de stock**
   - Entrées avec différentes quantités
   - Sorties avec vérification de stock
   - Validation des dates

4. **Rapports**
   - Génération mensuelle
   - Période personnalisée
   - Export CSV

### Script de test
```bash
# Ajouter des données d'exemple
python sample_data.py

# Lancer l'application
python main.py
```

## 🔄 Maintenance

### Sauvegarde de la base de données
```bash
# Copier le fichier SQLite
copy stock_management.db backup_YYYYMMDD.db
```

### Mise à jour du schéma
Pour ajouter de nouvelles colonnes :
```python
# Dans database.py, méthode init_database()
cursor.execute('ALTER TABLE produits ADD COLUMN nouvelle_colonne TEXT')
```

### Logs et débogage
- Ajouter des print() pour le débogage
- Utiliser try/catch pour gérer les erreurs
- Vérifier les permissions de fichiers

## 📈 Évolutions possibles

### Fonctionnalités à ajouter
1. **Gestion des fournisseurs**
   - Table fournisseurs
   - Liaison avec les entrées

2. **Alertes de stock**
   - Seuils minimum/maximum
   - Notifications automatiques

3. **Historique des modifications**
   - Table d'audit
   - Traçabilité des changements

4. **Import/Export avancé**
   - Import CSV/Excel
   - Export PDF avec graphiques

5. **Interface web**
   - Flask/Django
   - Accès multi-utilisateur

### Améliorations techniques
1. **Base de données**
   - Migration vers PostgreSQL/MySQL
   - Optimisation des requêtes
   - Sauvegarde automatique

2. **Interface**
   - Thèmes personnalisables
   - Interface responsive
   - Raccourcis clavier

3. **Sécurité**
   - Chiffrement de la base
   - Authentification à deux facteurs
   - Logs de sécurité

## 🐛 Problèmes connus

### Limitations actuelles
1. **Concurrence** : Pas de gestion multi-utilisateur simultané
2. **Validation** : Validation côté client uniquement
3. **Performance** : Pas optimisé pour de gros volumes
4. **Sauvegarde** : Pas de sauvegarde automatique

### Solutions de contournement
1. Utiliser un seul utilisateur à la fois
2. Valider manuellement les données importantes
3. Limiter le nombre d'enregistrements
4. Sauvegarder manuellement la base

---

**Version :** 1.0.0  
**Dernière mise à jour :** 2024  
**Développeur :** Assistant IA
