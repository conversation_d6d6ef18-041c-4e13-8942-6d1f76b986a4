import sqlite3
import hashlib
from datetime import datetime

class Database:
    def __init__(self, db_name="stock_management.db"):
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        return sqlite3.connect(self.db_name)
    
    def init_database(self):
        """Initialise la base de données avec les tables nécessaires"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Table des utilisateurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user'
            )
        ''')
        
        # Table des produits
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS produits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                nom TEXT NOT NULL,
                categorie TEXT,
                unite TEXT,
                stock_actuel REAL DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table des entrées
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS entrees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                produit_id INTEGER NOT NULL,
                date_entree TEXT NOT NULL,
                quantite REAL NOT NULL,
                source TEXT,
                note TEXT,
                user_id INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (produit_id) REFERENCES produits(id),
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        # Table des sorties
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sorties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                produit_id INTEGER NOT NULL,
                date_sortie TEXT NOT NULL,
                quantite REAL NOT NULL,
                destination TEXT,
                motif TEXT,
                user_id INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (produit_id) REFERENCES produits(id),
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        # Créer un utilisateur admin par défaut
        self.create_default_admin()
        
        conn.commit()
        conn.close()
    
    def create_default_admin(self):
        """Crée un utilisateur administrateur par défaut"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Vérifier si l'admin existe déjà
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if cursor.fetchone() is None:
            # Créer l'admin avec mot de passe hashé
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            cursor.execute(
                "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                ("admin", password_hash, "admin")
            )
            conn.commit()
        
        conn.close()
    
    def hash_password(self, password):
        """Hash un mot de passe"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_user(self, username, password):
        """Vérifie les identifiants d'un utilisateur"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = self.hash_password(password)
        cursor.execute(
            "SELECT id, username, role FROM users WHERE username = ? AND password = ?",
            (username, password_hash)
        )
        
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return {
                'id': user[0],
                'username': user[1],
                'role': user[2]
            }
        return None
    
    def add_user(self, username, password, role='user'):
        """Ajoute un nouvel utilisateur"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            password_hash = self.hash_password(password)
            cursor.execute(
                "INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                (username, password_hash, role)
            )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def add_product(self, code, nom, categorie, unite):
        """Ajoute un nouveau produit"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "INSERT INTO produits (code, nom, categorie, unite) VALUES (?, ?, ?, ?)",
                (code, nom, categorie, unite)
            )
            conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None
        finally:
            conn.close()
    
    def get_products(self):
        """Récupère tous les produits"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, code, nom, categorie, unite, stock_actuel 
            FROM produits 
            ORDER BY nom
        """)
        
        products = cursor.fetchall()
        conn.close()
        return products
    
    def get_product_by_id(self, product_id):
        """Récupère un produit par son ID"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute(
            "SELECT id, code, nom, categorie, unite, stock_actuel FROM produits WHERE id = ?",
            (product_id,)
        )
        
        product = cursor.fetchone()
        conn.close()
        return product
    
    def update_product(self, product_id, code, nom, categorie, unite):
        """Met à jour un produit"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "UPDATE produits SET code = ?, nom = ?, categorie = ?, unite = ? WHERE id = ?",
                (code, nom, categorie, unite, product_id)
            )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def delete_product(self, product_id):
        """Supprime un produit"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM produits WHERE id = ?", (product_id,))
        conn.commit()
        conn.close()
    
    def add_entry(self, produit_id, date_entree, quantite, source, note, user_id):
        """Ajoute une entrée de stock"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Ajouter l'entrée
        cursor.execute(
            "INSERT INTO entrees (produit_id, date_entree, quantite, source, note, user_id) VALUES (?, ?, ?, ?, ?, ?)",
            (produit_id, date_entree, quantite, source, note, user_id)
        )
        
        # Mettre à jour le stock
        cursor.execute(
            "UPDATE produits SET stock_actuel = stock_actuel + ? WHERE id = ?",
            (quantite, produit_id)
        )
        
        conn.commit()
        conn.close()
        return True
    
    def add_exit(self, produit_id, date_sortie, quantite, destination, motif, user_id):
        """Ajoute une sortie de stock"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Vérifier le stock disponible
        cursor.execute("SELECT stock_actuel FROM produits WHERE id = ?", (produit_id,))
        stock_actuel = cursor.fetchone()[0]
        
        if stock_actuel < quantite:
            conn.close()
            return False, "Stock insuffisant"
        
        # Ajouter la sortie
        cursor.execute(
            "INSERT INTO sorties (produit_id, date_sortie, quantite, destination, motif, user_id) VALUES (?, ?, ?, ?, ?, ?)",
            (produit_id, date_sortie, quantite, destination, motif, user_id)
        )
        
        # Mettre à jour le stock
        cursor.execute(
            "UPDATE produits SET stock_actuel = stock_actuel - ? WHERE id = ?",
            (quantite, produit_id)
        )
        
        conn.commit()
        conn.close()
        return True, "Sortie enregistrée avec succès"
    
    def get_entries(self, start_date=None, end_date=None):
        """Récupère les entrées avec filtres optionnels"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = """
            SELECT e.id, p.nom, e.date_entree, e.quantite, e.source, e.note, u.username
            FROM entrees e
            JOIN produits p ON e.produit_id = p.id
            LEFT JOIN users u ON e.user_id = u.id
        """
        
        params = []
        if start_date and end_date:
            query += " WHERE e.date_entree BETWEEN ? AND ?"
            params = [start_date, end_date]
        
        query += " ORDER BY e.date_entree DESC"
        
        cursor.execute(query, params)
        entries = cursor.fetchall()
        conn.close()
        return entries
    
    def get_exits(self, start_date=None, end_date=None):
        """Récupère les sorties avec filtres optionnels"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = """
            SELECT s.id, p.nom, s.date_sortie, s.quantite, s.destination, s.motif, u.username
            FROM sorties s
            JOIN produits p ON s.produit_id = p.id
            LEFT JOIN users u ON s.user_id = u.id
        """
        
        params = []
        if start_date and end_date:
            query += " WHERE s.date_sortie BETWEEN ? AND ?"
            params = [start_date, end_date]
        
        query += " ORDER BY s.date_sortie DESC"
        
        cursor.execute(query, params)
        exits = cursor.fetchall()
        conn.close()
        return exits
    
    def get_monthly_report(self, year, month):
        """Génère un rapport mensuel"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            end_date = f"{year + 1}-01-01"
        else:
            end_date = f"{year}-{month + 1:02d}-01"
        
        # Entrées du mois
        cursor.execute("""
            SELECT p.nom, SUM(e.quantite) as total_entree
            FROM entrees e
            JOIN produits p ON e.produit_id = p.id
            WHERE e.date_entree >= ? AND e.date_entree < ?
            GROUP BY p.id, p.nom
        """, (start_date, end_date))
        
        entries = cursor.fetchall()
        
        # Sorties du mois
        cursor.execute("""
            SELECT p.nom, SUM(s.quantite) as total_sortie
            FROM sorties s
            JOIN produits p ON s.produit_id = p.id
            WHERE s.date_sortie >= ? AND s.date_sortie < ?
            GROUP BY p.id, p.nom
        """, (start_date, end_date))
        
        exits = cursor.fetchall()
        
        conn.close()
        return entries, exits
