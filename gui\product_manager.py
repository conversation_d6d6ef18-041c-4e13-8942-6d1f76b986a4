import tkinter as tk
from tkinter import ttk, messagebox
from utils import Colors, show_error, show_success, confirm_action

class ProductManager:
    def __init__(self, parent, user, db):
        self.parent = parent
        self.user = user
        self.db = db
        
        self.create_widgets()
        self.refresh_products()
    
    def create_widgets(self):
        # Titre
        title_frame = tk.Frame(self.parent, bg=Colors.WHITE)
        title_frame.pack(fill=tk.X, padx=20, pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="🗂️ GESTION DES PRODUITS",
            font=("Arial", 18, "bold"),
            bg=Colors.WHITE,
            fg=Colors.PRIMARY
        )
        title_label.pack(side=tk.LEFT)
        
        # Boutons d'action (seulement pour admin)
        if self.user['role'] == 'admin':
            btn_frame = tk.Frame(title_frame, bg=Colors.WHITE)
            btn_frame.pack(side=tk.RIGHT)
            
            add_btn = tk.Button(
                btn_frame,
                text="➕ Nouveau Produit",
                font=("Arial", 10, "bold"),
                bg=Colors.SUCCESS,
                fg=Colors.WHITE,
                relief=tk.FLAT,
                padx=15,
                pady=8,
                command=self.show_add_product_dialog
            )
            add_btn.pack(side=tk.RIGHT, padx=5)
        
        # Frame de recherche
        search_frame = tk.Frame(self.parent, bg=Colors.WHITE)
        search_frame.pack(fill=tk.X, padx=20, pady=(0, 10))
        
        tk.Label(
            search_frame,
            text="🔍 Rechercher:",
            font=("Arial", 10),
            bg=Colors.WHITE
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_products)
        
        search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=("Arial", 10),
            width=30
        )
        search_entry.pack(side=tk.LEFT)
        
        # Tableau des produits
        self.create_products_table()
    
    def create_products_table(self):
        # Frame pour le tableau
        table_frame = tk.Frame(self.parent, bg=Colors.WHITE)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Colonnes
        columns = ("ID", "Code", "Nom", "Catégorie", "Unité", "Stock Actuel")
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # Configuration des colonnes
        self.tree.heading("ID", text="ID")
        self.tree.heading("Code", text="Code")
        self.tree.heading("Nom", text="Nom du Produit")
        self.tree.heading("Catégorie", text="Catégorie")
        self.tree.heading("Unité", text="Unité")
        self.tree.heading("Stock Actuel", text="Stock Actuel")
        
        # Largeur des colonnes
        self.tree.column("ID", width=50, anchor=tk.CENTER)
        self.tree.column("Code", width=100, anchor=tk.CENTER)
        self.tree.column("Nom", width=200, anchor=tk.W)
        self.tree.column("Catégorie", width=150, anchor=tk.CENTER)
        self.tree.column("Unité", width=100, anchor=tk.CENTER)
        self.tree.column("Stock Actuel", width=120, anchor=tk.CENTER)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Menu contextuel pour admin
        if self.user['role'] == 'admin':
            self.create_context_menu()
    
    def create_context_menu(self):
        self.context_menu = tk.Menu(self.parent, tearoff=0)
        self.context_menu.add_command(label="✏️ Modifier", command=self.edit_product)
        self.context_menu.add_command(label="🗑️ Supprimer", command=self.delete_product)
        
        self.tree.bind("<Button-3>", self.show_context_menu)
    
    def show_context_menu(self, event):
        # Sélectionner l'item sous le curseur
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def refresh_products(self):
        """Actualise la liste des produits"""
        # Vider le tableau
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Récupérer les produits
        products = self.db.get_products()
        
        for product in products:
            self.tree.insert("", tk.END, values=product)
    
    def filter_products(self, *args):
        """Filtre les produits selon la recherche"""
        search_term = self.search_var.get().lower()
        
        # Vider le tableau
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Récupérer tous les produits
        products = self.db.get_products()
        
        # Filtrer et afficher
        for product in products:
            if (search_term in str(product[1]).lower() or  # Code
                search_term in str(product[2]).lower() or  # Nom
                search_term in str(product[3]).lower()):   # Catégorie
                self.tree.insert("", tk.END, values=product)
    
    def show_add_product_dialog(self):
        """Affiche la boîte de dialogue d'ajout de produit"""
        self.product_dialog(mode="add")
    
    def edit_product(self):
        """Modifie le produit sélectionné"""
        selected = self.tree.selection()
        if not selected:
            show_error("Erreur", "Veuillez sélectionner un produit à modifier")
            return
        
        item = self.tree.item(selected[0])
        product_data = item['values']
        self.product_dialog(mode="edit", product_data=product_data)
    
    def delete_product(self):
        """Supprime le produit sélectionné"""
        selected = self.tree.selection()
        if not selected:
            show_error("Erreur", "Veuillez sélectionner un produit à supprimer")
            return
        
        item = self.tree.item(selected[0])
        product_data = item['values']
        
        if confirm_action("Confirmation", f"Êtes-vous sûr de vouloir supprimer le produit '{product_data[2]}' ?"):
            self.db.delete_product(product_data[0])
            show_success("Succès", "Produit supprimé avec succès")
            self.refresh_products()
    
    def product_dialog(self, mode="add", product_data=None):
        """Boîte de dialogue pour ajouter/modifier un produit"""
        dialog = tk.Toplevel(self.parent)
        dialog.title("Nouveau Produit" if mode == "add" else "Modifier Produit")
        dialog.geometry("400x300")
        dialog.configure(bg=Colors.LIGHT)
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # Centrer la boîte de dialogue
        dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        # Frame principal
        main_frame = tk.Frame(dialog, bg=Colors.LIGHT, padx=30, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title = "Nouveau Produit" if mode == "add" else "Modifier Produit"
        title_label = tk.Label(
            main_frame,
            text=title,
            font=("Arial", 14, "bold"),
            bg=Colors.LIGHT,
            fg=Colors.PRIMARY
        )
        title_label.pack(pady=(0, 20))
        
        # Champs
        fields = [
            ("Code:", "code"),
            ("Nom:", "nom"),
            ("Catégorie:", "categorie"),
            ("Unité:", "unite")
        ]
        
        entries = {}
        
        for label_text, field_name in fields:
            # Label
            label = tk.Label(
                main_frame,
                text=label_text,
                font=("Arial", 10),
                bg=Colors.LIGHT
            )
            label.pack(anchor=tk.W, pady=(10, 5))
            
            # Entry
            entry = tk.Entry(
                main_frame,
                font=("Arial", 10),
                relief=tk.FLAT,
                bd=5
            )
            entry.pack(fill=tk.X, pady=(0, 5))
            entries[field_name] = entry
            
            # Pré-remplir en mode édition
            if mode == "edit" and product_data:
                if field_name == "code":
                    entry.insert(0, product_data[1])
                elif field_name == "nom":
                    entry.insert(0, product_data[2])
                elif field_name == "categorie":
                    entry.insert(0, product_data[3])
                elif field_name == "unite":
                    entry.insert(0, product_data[4])
        
        # Boutons
        btn_frame = tk.Frame(main_frame, bg=Colors.LIGHT)
        btn_frame.pack(fill=tk.X, pady=(20, 0))
        
        cancel_btn = tk.Button(
            btn_frame,
            text="Annuler",
            font=("Arial", 10),
            bg=Colors.GRAY,
            fg=Colors.WHITE,
            relief=tk.FLAT,
            padx=20,
            pady=8,
            command=dialog.destroy
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        save_text = "Ajouter" if mode == "add" else "Modifier"
        save_btn = tk.Button(
            btn_frame,
            text=save_text,
            font=("Arial", 10, "bold"),
            bg=Colors.PRIMARY,
            fg=Colors.WHITE,
            relief=tk.FLAT,
            padx=20,
            pady=8,
            command=lambda: self.save_product(dialog, entries, mode, product_data)
        )
        save_btn.pack(side=tk.RIGHT)
        
        # Focus sur le premier champ
        entries["code"].focus()
    
    def save_product(self, dialog, entries, mode, product_data=None):
        """Sauvegarde le produit"""
        # Récupérer les valeurs
        code = entries["code"].get().strip()
        nom = entries["nom"].get().strip()
        categorie = entries["categorie"].get().strip()
        unite = entries["unite"].get().strip()
        
        # Validation
        if not all([code, nom, categorie, unite]):
            show_error("Erreur", "Veuillez remplir tous les champs")
            return
        
        try:
            if mode == "add":
                result = self.db.add_product(code, nom, categorie, unite)
                if result:
                    show_success("Succès", "Produit ajouté avec succès")
                    dialog.destroy()
                    self.refresh_products()
                else:
                    show_error("Erreur", "Ce code produit existe déjà")
            else:
                result = self.db.update_product(product_data[0], code, nom, categorie, unite)
                if result:
                    show_success("Succès", "Produit modifié avec succès")
                    dialog.destroy()
                    self.refresh_products()
                else:
                    show_error("Erreur", "Ce code produit existe déjà")
        except Exception as e:
            show_error("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")
