"""
Configuration de l'application de gestion de stock
"""

# Configuration de la base de données
DATABASE_NAME = "stock_management.db"

# Configuration de l'interface
WINDOW_TITLE = "Système de Gestion de Stock"
WINDOW_ICON = None  # Chemin vers l'icône (optionnel)

# Configuration des couleurs (peut être modifié)
COLORS = {
    'PRIMARY': "#2E86AB",
    'SECONDARY': "#A23B72", 
    'SUCCESS': "#F18F01",
    'DANGER': "#C73E1D",
    'LIGHT': "#F5F5F5",
    'DARK': "#333333",
    'WHITE': "#FFFFFF",
    'GRAY': "#6C757D"
}

# Configuration des rôles utilisateur
USER_ROLES = {
    'ADMIN': 'admin',
    'USER': 'user'
}

# Configuration par défaut de l'administrateur
DEFAULT_ADMIN = {
    'username': 'admin',
    'password': 'admin123',
    'role': 'admin'
}

# Configuration des rapports
REPORT_FORMATS = ['CSV', 'PDF']  # Formats d'export supportés
MAX_REPORT_ROWS = 10000  # Limite de lignes pour les rapports

# Configuration de l'application
APP_VERSION = "1.0.0"
APP_AUTHOR = "Assistant IA"
APP_DESCRIPTION = "Système de gestion de stock avec interface graphique"

# Configuration des logs (pour future implémentation)
LOG_LEVEL = "INFO"
LOG_FILE = "stock_management.log"

# Configuration de sauvegarde (pour future implémentation)
BACKUP_ENABLED = True
BACKUP_INTERVAL_DAYS = 7
BACKUP_FOLDER = "backups"
