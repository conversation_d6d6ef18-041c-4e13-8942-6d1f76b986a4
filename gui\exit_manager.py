import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from utils import Colors, show_error, show_success, get_current_date, validate_date, validate_number

class ExitManager:
    def __init__(self, parent, user, db):
        self.parent = parent
        self.user = user
        self.db = db
        
        self.create_widgets()
        self.refresh_exits()
        self.load_products()
    
    def create_widgets(self):
        # Titre
        title_frame = tk.Frame(self.parent, bg=Colors.WHITE)
        title_frame.pack(fill=tk.X, padx=20, pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="➖ GESTION DES SORTIES",
            font=("Arial", 18, "bold"),
            bg=Colors.WHITE,
            fg=Colors.DANGER
        )
        title_label.pack(side=tk.LEFT)
        
        # Bouton nouvelle sortie
        add_btn = tk.Button(
            title_frame,
            text="➖ Nouvelle Sortie",
            font=("Arial", 10, "bold"),
            bg=Colors.DANGER,
            fg=Colors.WHITE,
            relief=tk.FLAT,
            padx=15,
            pady=8,
            command=self.show_add_exit_dialog
        )
        add_btn.pack(side=tk.RIGHT)
        
        # Frame de filtres
        filter_frame = tk.Frame(self.parent, bg=Colors.WHITE)
        filter_frame.pack(fill=tk.X, padx=20, pady=(0, 10))
        
        # Filtre par date
        tk.Label(
            filter_frame,
            text="📅 Du:",
            font=("Arial", 10),
            bg=Colors.WHITE
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        self.start_date_var = tk.StringVar(value=get_current_date())
        start_date_entry = tk.Entry(
            filter_frame,
            textvariable=self.start_date_var,
            font=("Arial", 10),
            width=12
        )
        start_date_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        tk.Label(
            filter_frame,
            text="Au:",
            font=("Arial", 10),
            bg=Colors.WHITE
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        self.end_date_var = tk.StringVar(value=get_current_date())
        end_date_entry = tk.Entry(
            filter_frame,
            textvariable=self.end_date_var,
            font=("Arial", 10),
            width=12
        )
        end_date_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        filter_btn = tk.Button(
            filter_frame,
            text="🔍 Filtrer",
            font=("Arial", 9),
            bg=Colors.PRIMARY,
            fg=Colors.WHITE,
            relief=tk.FLAT,
            padx=10,
            pady=5,
            command=self.filter_exits
        )
        filter_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        reset_btn = tk.Button(
            filter_frame,
            text="🔄 Tout afficher",
            font=("Arial", 9),
            bg=Colors.GRAY,
            fg=Colors.WHITE,
            relief=tk.FLAT,
            padx=10,
            pady=5,
            command=self.refresh_exits
        )
        reset_btn.pack(side=tk.LEFT)
        
        # Tableau des sorties
        self.create_exits_table()
    
    def create_exits_table(self):
        # Frame pour le tableau
        table_frame = tk.Frame(self.parent, bg=Colors.WHITE)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Colonnes
        columns = ("ID", "Produit", "Date", "Quantité", "Destination", "Motif", "Utilisateur")
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # Configuration des colonnes
        self.tree.heading("ID", text="ID")
        self.tree.heading("Produit", text="Produit")
        self.tree.heading("Date", text="Date de Sortie")
        self.tree.heading("Quantité", text="Quantité")
        self.tree.heading("Destination", text="Destination")
        self.tree.heading("Motif", text="Motif")
        self.tree.heading("Utilisateur", text="Utilisateur")
        
        # Largeur des colonnes
        self.tree.column("ID", width=50, anchor=tk.CENTER)
        self.tree.column("Produit", width=200, anchor=tk.W)
        self.tree.column("Date", width=100, anchor=tk.CENTER)
        self.tree.column("Quantité", width=100, anchor=tk.CENTER)
        self.tree.column("Destination", width=150, anchor=tk.W)
        self.tree.column("Motif", width=200, anchor=tk.W)
        self.tree.column("Utilisateur", width=100, anchor=tk.CENTER)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def load_products(self):
        """Charge la liste des produits avec leur stock"""
        self.products = self.db.get_products()
        self.product_dict = {f"{p[1]} - {p[2]} (Stock: {p[5]})": p[0] for p in self.products}
        self.product_stock = {p[0]: p[5] for p in self.products}
    
    def refresh_exits(self):
        """Actualise la liste des sorties"""
        # Vider le tableau
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Récupérer les sorties
        exits = self.db.get_exits()
        
        for exit_item in exits:
            # Formater la date pour l'affichage
            date_formatted = datetime.strptime(exit_item[2], '%Y-%m-%d').strftime('%d/%m/%Y')
            exit_formatted = list(exit_item)
            exit_formatted[2] = date_formatted
            self.tree.insert("", tk.END, values=exit_formatted)
    
    def filter_exits(self):
        """Filtre les sorties par date"""
        start_date = self.start_date_var.get()
        end_date = self.end_date_var.get()
        
        if not validate_date(start_date) or not validate_date(end_date):
            show_error("Erreur", "Format de date invalide. Utilisez YYYY-MM-DD")
            return
        
        # Vider le tableau
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Récupérer les sorties filtrées
        exits = self.db.get_exits(start_date, end_date)
        
        for exit_item in exits:
            # Formater la date pour l'affichage
            date_formatted = datetime.strptime(exit_item[2], '%Y-%m-%d').strftime('%d/%m/%Y')
            exit_formatted = list(exit_item)
            exit_formatted[2] = date_formatted
            self.tree.insert("", tk.END, values=exit_formatted)
    
    def show_add_exit_dialog(self):
        """Affiche la boîte de dialogue d'ajout de sortie"""
        dialog = tk.Toplevel(self.parent)
        dialog.title("Nouvelle Sortie de Stock")
        dialog.geometry("450x400")
        dialog.configure(bg=Colors.LIGHT)
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # Centrer la boîte de dialogue
        dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        # Frame principal
        main_frame = tk.Frame(dialog, bg=Colors.LIGHT, padx=30, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title_label = tk.Label(
            main_frame,
            text="➖ Nouvelle Sortie de Stock",
            font=("Arial", 14, "bold"),
            bg=Colors.LIGHT,
            fg=Colors.DANGER
        )
        title_label.pack(pady=(0, 20))
        
        # Sélection du produit
        tk.Label(
            main_frame,
            text="Produit:",
            font=("Arial", 10),
            bg=Colors.LIGHT
        ).pack(anchor=tk.W, pady=(10, 5))
        
        product_var = tk.StringVar()
        product_combo = ttk.Combobox(
            main_frame,
            textvariable=product_var,
            values=list(self.product_dict.keys()),
            state="readonly",
            font=("Arial", 10)
        )
        product_combo.pack(fill=tk.X, pady=(0, 10))
        
        # Affichage du stock disponible
        self.stock_label = tk.Label(
            main_frame,
            text="Stock disponible: -",
            font=("Arial", 10, "italic"),
            bg=Colors.LIGHT,
            fg=Colors.GRAY
        )
        self.stock_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Bind pour mettre à jour le stock affiché
        product_combo.bind('<<ComboboxSelected>>', lambda e: self.update_stock_display(product_var))
        
        # Date de sortie
        tk.Label(
            main_frame,
            text="Date de sortie (YYYY-MM-DD):",
            font=("Arial", 10),
            bg=Colors.LIGHT
        ).pack(anchor=tk.W, pady=(10, 5))
        
        date_var = tk.StringVar(value=get_current_date())
        date_entry = tk.Entry(
            main_frame,
            textvariable=date_var,
            font=("Arial", 10),
            relief=tk.FLAT,
            bd=5
        )
        date_entry.pack(fill=tk.X, pady=(0, 10))
        
        # Quantité
        tk.Label(
            main_frame,
            text="Quantité:",
            font=("Arial", 10),
            bg=Colors.LIGHT
        ).pack(anchor=tk.W, pady=(10, 5))
        
        quantity_var = tk.StringVar()
        quantity_entry = tk.Entry(
            main_frame,
            textvariable=quantity_var,
            font=("Arial", 10),
            relief=tk.FLAT,
            bd=5
        )
        quantity_entry.pack(fill=tk.X, pady=(0, 10))
        
        # Destination
        tk.Label(
            main_frame,
            text="Destination (optionnel):",
            font=("Arial", 10),
            bg=Colors.LIGHT
        ).pack(anchor=tk.W, pady=(10, 5))
        
        destination_var = tk.StringVar()
        destination_entry = tk.Entry(
            main_frame,
            textvariable=destination_var,
            font=("Arial", 10),
            relief=tk.FLAT,
            bd=5
        )
        destination_entry.pack(fill=tk.X, pady=(0, 10))
        
        # Motif
        tk.Label(
            main_frame,
            text="Motif (optionnel):",
            font=("Arial", 10),
            bg=Colors.LIGHT
        ).pack(anchor=tk.W, pady=(10, 5))
        
        motif_text = tk.Text(
            main_frame,
            height=3,
            font=("Arial", 10),
            relief=tk.FLAT,
            bd=5
        )
        motif_text.pack(fill=tk.X, pady=(0, 20))
        
        # Boutons
        btn_frame = tk.Frame(main_frame, bg=Colors.LIGHT)
        btn_frame.pack(fill=tk.X)
        
        cancel_btn = tk.Button(
            btn_frame,
            text="Annuler",
            font=("Arial", 10),
            bg=Colors.GRAY,
            fg=Colors.WHITE,
            relief=tk.FLAT,
            padx=20,
            pady=8,
            command=dialog.destroy
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        save_btn = tk.Button(
            btn_frame,
            text="Enregistrer",
            font=("Arial", 10, "bold"),
            bg=Colors.DANGER,
            fg=Colors.WHITE,
            relief=tk.FLAT,
            padx=20,
            pady=8,
            command=lambda: self.save_exit(
                dialog, product_var, date_var, quantity_var, 
                destination_var, motif_text
            )
        )
        save_btn.pack(side=tk.RIGHT)
        
        # Focus sur le produit
        product_combo.focus()
    
    def update_stock_display(self, product_var):
        """Met à jour l'affichage du stock disponible"""
        product_selection = product_var.get()
        if product_selection and product_selection in self.product_dict:
            product_id = self.product_dict[product_selection]
            stock = self.product_stock.get(product_id, 0)
            self.stock_label.config(text=f"Stock disponible: {stock}")
        else:
            self.stock_label.config(text="Stock disponible: -")
    
    def save_exit(self, dialog, product_var, date_var, quantity_var, destination_var, motif_text):
        """Sauvegarde la sortie"""
        # Récupérer les valeurs
        product_selection = product_var.get()
        date_sortie = date_var.get().strip()
        quantity_str = quantity_var.get().strip()
        destination = destination_var.get().strip()
        motif = motif_text.get("1.0", tk.END).strip()
        
        # Validation
        if not product_selection:
            show_error("Erreur", "Veuillez sélectionner un produit")
            return
        
        if not date_sortie:
            show_error("Erreur", "Veuillez saisir une date")
            return
        
        if not validate_date(date_sortie):
            show_error("Erreur", "Format de date invalide. Utilisez YYYY-MM-DD")
            return
        
        if not quantity_str:
            show_error("Erreur", "Veuillez saisir une quantité")
            return
        
        if not validate_number(quantity_str):
            show_error("Erreur", "La quantité doit être un nombre positif")
            return
        
        try:
            # Récupérer l'ID du produit
            product_id = self.product_dict[product_selection]
            quantity = float(quantity_str)
            
            # Vérifier le stock disponible
            stock_disponible = self.product_stock.get(product_id, 0)
            if quantity > stock_disponible:
                show_error("Erreur", f"Stock insuffisant. Stock disponible: {stock_disponible}")
                return
            
            # Enregistrer la sortie
            success, message = self.db.add_exit(
                product_id, date_sortie, quantity, destination, motif, self.user['id']
            )
            
            if success:
                show_success("Succès", "Sortie enregistrée avec succès")
                dialog.destroy()
                self.refresh_exits()
                self.load_products()  # Recharger pour mettre à jour les stocks
            else:
                show_error("Erreur", message)
                
        except Exception as e:
            show_error("Erreur", f"Erreur lors de la sauvegarde: {str(e)}")
