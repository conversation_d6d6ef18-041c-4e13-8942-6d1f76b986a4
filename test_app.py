#!/usr/bin/env python3
"""
Script de test pour vérifier le bon fonctionnement de l'application
"""

import sys
import os
import sqlite3
from datetime import datetime

# Ajouter le répertoire courant au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Database
from models import User, Product, Entry, Exit
import utils

def test_database():
    """Test des fonctionnalités de base de données"""
    print("🧪 Test de la base de données...")
    
    # Créer une base de test
    db = Database("test_stock.db")
    
    try:
        # Test 1: Vérification de l'admin par défaut
        admin = db.verify_user("admin", "admin123")
        assert admin is not None, "Admin par défaut non trouvé"
        assert admin['role'] == 'admin', "Rôle admin incorrect"
        print("✅ Admin par défaut OK")
        
        # Test 2: Ajout d'un produit
        product_id = db.add_product("TEST001", "Produit Test", "Test", "Unité")
        assert product_id is not None, "Échec ajout produit"
        print("✅ Ajout produit OK")
        
        # Test 3: Récupération des produits
        products = db.get_products()
        assert len(products) > 0, "Aucun produit trouvé"
        print("✅ Récupération produits OK")
        
        # Test 4: Ajout d'une entrée
        success = db.add_entry(product_id, "2024-01-01", 10, "Test", "Note test", 1)
        assert success, "Échec ajout entrée"
        print("✅ Ajout entrée OK")
        
        # Test 5: Vérification du stock
        product = db.get_product_by_id(product_id)
        assert product[5] == 10, f"Stock incorrect: {product[5]} au lieu de 10"
        print("✅ Mise à jour stock OK")
        
        # Test 6: Ajout d'une sortie
        success, message = db.add_exit(product_id, "2024-01-02", 3, "Test", "Sortie test", 1)
        assert success, f"Échec ajout sortie: {message}"
        print("✅ Ajout sortie OK")
        
        # Test 7: Vérification du stock après sortie
        product = db.get_product_by_id(product_id)
        assert product[5] == 7, f"Stock incorrect après sortie: {product[5]} au lieu de 7"
        print("✅ Mise à jour stock après sortie OK")
        
        # Test 8: Test de sortie avec stock insuffisant
        success, message = db.add_exit(product_id, "2024-01-03", 10, "Test", "Sortie impossible", 1)
        assert not success, "Sortie avec stock insuffisant autorisée"
        print("✅ Contrôle stock insuffisant OK")
        
        print("🎉 Tous les tests de base de données sont passés!")
        
    except AssertionError as e:
        print(f"❌ Échec du test: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False
    finally:
        # Nettoyer la base de test
        if os.path.exists("test_stock.db"):
            os.remove("test_stock.db")
    
    return True

def test_utils():
    """Test des fonctions utilitaires"""
    print("\n🧪 Test des utilitaires...")
    
    try:
        # Test validation de date
        assert utils.validate_date("2024-01-01"), "Date valide rejetée"
        assert not utils.validate_date("01/01/2024"), "Date invalide acceptée"
        assert not utils.validate_date("2024-13-01"), "Date impossible acceptée"
        print("✅ Validation dates OK")
        
        # Test validation de nombre
        assert utils.validate_number("10"), "Nombre valide rejeté"
        assert utils.validate_number("10.5"), "Nombre décimal valide rejeté"
        assert not utils.validate_number("-5"), "Nombre négatif accepté"
        assert not utils.validate_number("abc"), "Texte accepté comme nombre"
        print("✅ Validation nombres OK")
        
        # Test formatage de date
        formatted = utils.format_date_for_display("2024-01-01")
        assert formatted == "01/01/2024", f"Format incorrect: {formatted}"
        print("✅ Formatage dates OK")
        
        # Test date actuelle
        current = utils.get_current_date()
        assert utils.validate_date(current), "Date actuelle invalide"
        print("✅ Date actuelle OK")
        
        print("🎉 Tous les tests d'utilitaires sont passés!")
        
    except AssertionError as e:
        print(f"❌ Échec du test: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False
    
    return True

def test_models():
    """Test des modèles de données"""
    print("\n🧪 Test des modèles...")
    
    try:
        # Test User
        user = User(1, "test", "admin")
        assert user.id == 1, "ID utilisateur incorrect"
        assert user.username == "test", "Nom utilisateur incorrect"
        print("✅ Modèle User OK")
        
        # Test Product
        product = Product(1, "TEST001", "Produit Test", "Catégorie", "Unité", 10.0)
        assert product.stock_actuel == 10.0, "Stock produit incorrect"
        print("✅ Modèle Product OK")
        
        # Test Entry
        entry = Entry(1, 1, "2024-01-01", 5.0, "Source", "Note", 1)
        assert entry.quantite == 5.0, "Quantité entrée incorrecte"
        print("✅ Modèle Entry OK")
        
        # Test Exit
        exit_item = Exit(1, 1, "2024-01-01", 3.0, "Destination", "Motif", 1)
        assert exit_item.quantite == 3.0, "Quantité sortie incorrecte"
        print("✅ Modèle Exit OK")
        
        print("🎉 Tous les tests de modèles sont passés!")
        
    except AssertionError as e:
        print(f"❌ Échec du test: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False
    
    return True

def test_imports():
    """Test des imports de modules"""
    print("\n🧪 Test des imports...")
    
    try:
        # Test import des modules GUI
        from gui.login_window import LoginWindow
        from gui.main_window import MainWindow
        from gui.product_manager import ProductManager
        from gui.entry_manager import EntryManager
        from gui.exit_manager import ExitManager
        from gui.reports import ReportsManager
        print("✅ Imports GUI OK")
        
        # Test import des autres modules
        import database
        import models
        import utils
        import config
        print("✅ Imports modules OK")
        
        print("🎉 Tous les tests d'imports sont passés!")
        
    except ImportError as e:
        print(f"❌ Échec d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False
    
    return True

def main():
    """Fonction principale de test"""
    print("🚀 Démarrage des tests de l'application...")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Modèles", test_models),
        ("Utilitaires", test_utils),
        ("Base de données", test_database)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Test: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
        else:
            print(f"❌ Échec du test: {test_name}")
    
    print("\n" + "=" * 50)
    print(f"📊 Résultats: {passed}/{total} tests passés")
    
    if passed == total:
        print("🎉 Tous les tests sont passés! L'application est prête.")
        return True
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
