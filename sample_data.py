"""
Script pour ajouter des données d'exemple à la base de données
Utile pour tester l'application avec des données réalistes
"""

from database import Database
from datetime import datetime, timedelta
import random

def add_sample_data():
    """Ajoute des données d'exemple à la base de données"""
    db = Database()
    
    print("Ajout de données d'exemple...")
    
    # Produits d'exemple
    sample_products = [
        ("ORDI001", "Ordinateur portable Dell", "Informatique", "Unité"),
        ("SOUR001", "Souris optique", "Informatique", "Unité"),
        ("CLAV001", "Clavier sans fil", "Informatique", "Unité"),
        ("ECRA001", "Écran 24 pouces", "Informatique", "Unité"),
        ("IMPR001", "Imprimante laser", "Informatique", "Unité"),
        ("PAPI001", "Papier A4", "Bureautique", "<PERSON><PERSON>"),
        ("STYLO01", "<PERSON>ylo bille bleu", "Bureautique", "Boîte"),
        ("CRAY001", "Crayon HB", "Bureautique", "Boîte"),
        ("AGRA001", "Agrafeuse", "Bureautique", "Unité"),
        ("CLAS001", "Classeur A4", "Bureautique", "Unité"),
        ("TABL001", "Table de bureau", "Mobilier", "Unité"),
        ("CHAI001", "Chaise de bureau", "Mobilier", "Unité"),
        ("ARMO001", "Armoire métallique", "Mobilier", "Unité"),
        ("LAMP001", "Lampe de bureau", "Mobilier", "Unité"),
        ("TELE001", "Téléphone fixe", "Communication", "Unité")
    ]
    
    # Ajouter les produits
    product_ids = []
    for code, nom, categorie, unite in sample_products:
        product_id = db.add_product(code, nom, categorie, unite)
        if product_id:
            product_ids.append(product_id)
            print(f"✓ Produit ajouté: {nom}")
    
    # Ajouter des entrées d'exemple
    sources = ["Fournisseur A", "Fournisseur B", "Achat direct", "Donation", "Transfert"]
    
    for _ in range(30):  # 30 entrées aléatoires
        product_id = random.choice(product_ids)
        date_entree = (datetime.now() - timedelta(days=random.randint(1, 90))).strftime('%Y-%m-%d')
        quantite = random.randint(1, 20)
        source = random.choice(sources)
        note = f"Entrée automatique - Test {random.randint(1, 100)}"
        
        db.add_entry(product_id, date_entree, quantite, source, note, 1)  # User ID 1 (admin)
    
    print(f"✓ {30} entrées ajoutées")
    
    # Ajouter des sorties d'exemple
    destinations = ["Service IT", "Comptabilité", "Direction", "Accueil", "Maintenance"]
    motifs = ["Attribution", "Remplacement", "Réparation", "Prêt", "Formation"]
    
    for _ in range(20):  # 20 sorties aléatoires
        product_id = random.choice(product_ids)
        
        # Vérifier le stock disponible
        product = db.get_product_by_id(product_id)
        if product and product[5] > 0:  # Si stock > 0
            date_sortie = (datetime.now() - timedelta(days=random.randint(1, 60))).strftime('%Y-%m-%d')
            quantite = min(random.randint(1, 5), product[5])  # Ne pas dépasser le stock
            destination = random.choice(destinations)
            motif = random.choice(motifs)
            
            success, message = db.add_exit(product_id, date_sortie, quantite, destination, motif, 1)
            if success:
                print(f"✓ Sortie ajoutée pour {product[2]}")
    
    print("✓ Données d'exemple ajoutées avec succès!")
    print("\nVous pouvez maintenant tester l'application avec ces données.")
    print("Connectez-vous avec: admin / admin123")

if __name__ == "__main__":
    add_sample_data()
