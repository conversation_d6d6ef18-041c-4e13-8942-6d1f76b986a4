import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from database import Database
from utils import (
    center_window, Colors, Fonts, Spacing, get_current_datetime,
    create_modern_button, create_card_frame, create_icon_label
)
from gui.product_manager import ProductManager
from gui.entry_manager import EntryManager
from gui.exit_manager import ExitManager
from gui.reports import ReportsManager

class MainWindow:
    def __init__(self, user):
        self.root = tk.Tk()
        self.root.title("📦 Système de Gestion de Stock - Interface Moderne")
        self.root.configure(bg=Colors.BACKGROUND)

        # Maximiser la fenêtre
        self.root.state('zoomed')

        # Variables d'instance
        self.user = user
        self.db = Database()
        self.current_module = None
        self.active_menu_btn = None

        # Créer l'interface
        self.create_widgets()
        self.show_products()  # Afficher les produits par défaut

    def create_widgets(self):
        # Barre supérieure moderne
        self.create_modern_header()

        # Conteneur principal
        main_container = tk.Frame(self.root, bg=Colors.BACKGROUND)
        main_container.pack(fill=tk.BOTH, expand=True)

        # Barre latérale moderne
        self.create_modern_sidebar(main_container)

        # Zone de contenu avec carte
        self.create_modern_content_area(main_container)

    def create_modern_header(self):
        """Crée un en-tête moderne avec dégradé"""
        # En-tête principal avec dégradé
        header_frame = tk.Frame(self.root, bg=Colors.PRIMARY, height=Spacing.HEADER_HEIGHT)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Conteneur du contenu de l'en-tête
        header_content = tk.Frame(header_frame, bg=Colors.PRIMARY)
        header_content.pack(fill=tk.BOTH, expand=True, padx=Spacing.LG, pady=Spacing.MD)

        # Section gauche - Logo et titre
        left_section = tk.Frame(header_content, bg=Colors.PRIMARY)
        left_section.pack(side=tk.LEFT, fill=tk.Y)

        # Logo avec animation
        logo_frame = tk.Frame(left_section, bg=Colors.PRIMARY_DARK, width=40, height=40)
        logo_frame.pack_propagate(False)
        logo_frame.pack(side=tk.LEFT, padx=(0, Spacing.MD))

        logo_label = tk.Label(
            logo_frame,
            text="📦",
            font=(Fonts.PRIMARY_FONT, 20),
            bg=Colors.PRIMARY_DARK,
            fg=Colors.WHITE
        )
        logo_label.pack(expand=True)

        # Titre et sous-titre
        title_frame = tk.Frame(left_section, bg=Colors.PRIMARY)
        title_frame.pack(side=tk.LEFT, fill=tk.Y)

        title_label = tk.Label(
            title_frame,
            text="GESTION DE STOCK",
            font=(Fonts.PRIMARY_FONT, Fonts.SUBTITLE_SIZE, "bold"),
            bg=Colors.PRIMARY,
            fg=Colors.WHITE
        )
        title_label.pack(anchor=tk.W)

        subtitle_label = tk.Label(
            title_frame,
            text="Interface Moderne & Intuitive",
            font=(Fonts.PRIMARY_FONT, Fonts.SMALL_SIZE),
            bg=Colors.PRIMARY,
            fg=Colors.PRIMARY_LIGHT
        )
        subtitle_label.pack(anchor=tk.W)

        # Section droite - Informations utilisateur
        right_section = tk.Frame(header_content, bg=Colors.PRIMARY)
        right_section.pack(side=tk.RIGHT, fill=tk.Y)

        # Carte utilisateur
        user_card = tk.Frame(
            right_section,
            bg=Colors.PRIMARY_DARK,
            relief=tk.FLAT,
            bd=1,
            highlightbackground=Colors.PRIMARY_LIGHT,
            highlightthickness=1
        )
        user_card.pack(side=tk.RIGHT, padx=Spacing.MD, pady=Spacing.XS)

        user_content = tk.Frame(user_card, bg=Colors.PRIMARY_DARK)
        user_content.pack(padx=Spacing.MD, pady=Spacing.SM)

        # Icône utilisateur
        user_icon = tk.Label(
            user_content,
            text="👤",
            font=(Fonts.PRIMARY_FONT, Fonts.HEADING_SIZE),
            bg=Colors.PRIMARY_DARK,
            fg=Colors.WHITE
        )
        user_icon.pack(side=tk.LEFT, padx=(0, Spacing.SM))

        # Informations utilisateur
        user_info_frame = tk.Frame(user_content, bg=Colors.PRIMARY_DARK)
        user_info_frame.pack(side=tk.LEFT)

        user_name = tk.Label(
            user_info_frame,
            text=f"{self.user['username']}",
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE, "bold"),
            bg=Colors.PRIMARY_DARK,
            fg=Colors.WHITE
        )
        user_name.pack(anchor=tk.W)

        user_role = tk.Label(
            user_info_frame,
            text=f"🛡️ {self.user['role'].upper()}",
            font=(Fonts.PRIMARY_FONT, Fonts.SMALL_SIZE),
            bg=Colors.PRIMARY_DARK,
            fg=Colors.PRIMARY_LIGHT
        )
        user_role.pack(anchor=tk.W)

        # Date et heure
        datetime_label = tk.Label(
            user_content,
            text=f"📅 {get_current_datetime()}",
            font=(Fonts.PRIMARY_FONT, Fonts.SMALL_SIZE),
            bg=Colors.PRIMARY_DARK,
            fg=Colors.PRIMARY_LIGHT
        )
        datetime_label.pack(side=tk.RIGHT, padx=(Spacing.MD, 0))

    def create_modern_sidebar(self, parent):
        """Crée une barre latérale moderne"""
        # Conteneur de la sidebar
        sidebar_container = tk.Frame(parent, bg=Colors.BACKGROUND)
        sidebar_container.pack(side=tk.LEFT, fill=tk.Y, padx=(Spacing.MD, 0), pady=Spacing.MD)

        # Carte de la sidebar
        sidebar_card = create_card_frame(sidebar_container, Colors.SURFACE, shadow=True)
        sidebar_card.pack(fill=tk.Y, expand=True)

        # Contenu de la sidebar
        sidebar_content = tk.Frame(sidebar_card, bg=Colors.SURFACE, width=Spacing.SIDEBAR_WIDTH)
        sidebar_content.pack(fill=tk.BOTH, expand=True, padx=Spacing.MD, pady=Spacing.LG)
        sidebar_content.pack_propagate(False)

        # Titre de navigation
        nav_title = tk.Label(
            sidebar_content,
            text="🧭 NAVIGATION",
            font=(Fonts.PRIMARY_FONT, Fonts.HEADING_SIZE, "bold"),
            bg=Colors.SURFACE,
            fg=Colors.TEXT_PRIMARY
        )
        nav_title.pack(pady=(0, Spacing.LG))

        # Boutons de menu modernes
        self.menu_buttons = []
        menu_items = [
            ("🗂️", "Produits", self.show_products, Colors.INFO),
            ("➕", "Entrées", self.show_entries, Colors.SUCCESS),
            ("➖", "Sorties", self.show_exits, Colors.WARNING),
            ("📈", "Rapports", self.show_reports, Colors.PRIMARY),
        ]

        # Ajouter gestion des utilisateurs pour admin
        if self.user['role'] == 'admin':
            menu_items.append(("👥", "Utilisateurs", self.show_users, Colors.SECONDARY))

        menu_items.append(("🔓", "Déconnexion", self.logout, Colors.DANGER))

        for icon, text, command, color in menu_items:
            btn_frame = tk.Frame(sidebar_content, bg=Colors.SURFACE)
            btn_frame.pack(fill=tk.X, pady=Spacing.XS)

            btn = tk.Button(
                btn_frame,
                text=f"{icon}  {text}",
                font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE, "bold"),
                bg=Colors.SURFACE,
                fg=Colors.TEXT_PRIMARY,
                relief=tk.FLAT,
                bd=0,
                padx=Spacing.MD,
                pady=Spacing.MD,
                anchor=tk.W,
                command=lambda cmd=command, btn_ref=btn_frame: self.handle_menu_click(cmd, btn_ref, color),
                cursor="hand2"
            )
            btn.pack(fill=tk.X)

            # Stocker la référence du bouton
            self.menu_buttons.append((btn, btn_frame, color))

            # Effets hover
            def create_hover_effects(button, frame, hover_color):
                def on_enter(e):
                    if frame != self.active_menu_btn:
                        button.configure(bg=hover_color, fg=Colors.WHITE)
                        frame.configure(bg=hover_color)

                def on_leave(e):
                    if frame != self.active_menu_btn:
                        button.configure(bg=Colors.SURFACE, fg=Colors.TEXT_PRIMARY)
                        frame.configure(bg=Colors.SURFACE)

                button.bind("<Enter>", on_enter)
                button.bind("<Leave>", on_leave)

            create_hover_effects(btn, btn_frame, color)

    def create_modern_content_area(self, parent):
        """Crée une zone de contenu moderne"""
        # Conteneur de contenu
        content_container = tk.Frame(parent, bg=Colors.BACKGROUND)
        content_container.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=Spacing.MD, pady=Spacing.MD)

        # Carte de contenu
        content_card = create_card_frame(content_container, Colors.SURFACE, shadow=True)
        content_card.pack(fill=tk.BOTH, expand=True)

        # Zone de contenu
        self.content_frame = tk.Frame(content_card, bg=Colors.SURFACE)
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=Spacing.LG, pady=Spacing.LG)

    def handle_menu_click(self, command, btn_frame, color):
        """Gère le clic sur un bouton de menu"""
        # Réinitialiser l'ancien bouton actif
        if self.active_menu_btn:
            for btn, frame, _ in self.menu_buttons:
                if frame == self.active_menu_btn:
                    btn.configure(bg=Colors.SURFACE, fg=Colors.TEXT_PRIMARY)
                    frame.configure(bg=Colors.SURFACE)
                    break

        # Activer le nouveau bouton
        self.active_menu_btn = btn_frame
        for btn, frame, _ in self.menu_buttons:
            if frame == btn_frame:
                btn.configure(bg=color, fg=Colors.WHITE)
                frame.configure(bg=color)
                break

        # Exécuter la commande
        command()



    def create_sidebar(self, parent):
        sidebar_frame = tk.Frame(parent, bg=Colors.DARK, width=200)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        sidebar_frame.pack_propagate(False)

        # Titre de la sidebar
        sidebar_title = tk.Label(
            sidebar_frame,
            text="MENU",
            font=("Arial", 12, "bold"),
            bg=Colors.DARK,
            fg=Colors.WHITE,
            pady=20
        )
        sidebar_title.pack(fill=tk.X)

        # Boutons du menu
        menu_buttons = [
            ("🗂️ Produits", self.show_products),
            ("➕ Entrées", self.show_entries),
            ("➖ Sorties", self.show_exits),
            ("📊 Rapports", self.show_reports),
        ]

        # Ajouter gestion des utilisateurs pour admin
        if self.user['role'] == 'admin':
            menu_buttons.append(("👥 Utilisateurs", self.show_users))

        menu_buttons.append(("🔓 Déconnexion", self.logout))

        for text, command in menu_buttons:
            btn = tk.Button(
                sidebar_frame,
                text=text,
                font=("Arial", 11),
                bg=Colors.DARK,
                fg=Colors.WHITE,
                relief=tk.FLAT,
                padx=20,
                pady=15,
                command=command,
                anchor=tk.W
            )
            btn.pack(fill=tk.X, padx=10, pady=2)

            # Effet hover
            btn.bind("<Enter>", lambda e, b=btn: b.configure(bg=Colors.PRIMARY))
            btn.bind("<Leave>", lambda e, b=btn: b.configure(bg=Colors.DARK))

    def create_content_area(self, parent):
        self.content_frame = tk.Frame(parent, bg=Colors.WHITE, relief=tk.RAISED, bd=1)
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

    def clear_content(self):
        """Vide la zone de contenu"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_products(self):
        """Affiche le gestionnaire de produits"""
        self.clear_content()
        self.current_module = ProductManager(self.content_frame, self.user, self.db)

    def show_entries(self):
        """Affiche le gestionnaire d'entrées"""
        self.clear_content()
        self.current_module = EntryManager(self.content_frame, self.user, self.db)

    def show_exits(self):
        """Affiche le gestionnaire de sorties"""
        self.clear_content()
        self.current_module = ExitManager(self.content_frame, self.user, self.db)

    def show_reports(self):
        """Affiche le gestionnaire de rapports"""
        self.clear_content()
        self.current_module = ReportsManager(self.content_frame, self.user, self.db)

    def show_users(self):
        """Affiche le gestionnaire d'utilisateurs (admin seulement)"""
        if self.user['role'] != 'admin':
            messagebox.showerror("Erreur", "Accès non autorisé")
            return

        self.clear_content()
        # TODO: Implémenter le gestionnaire d'utilisateurs
        label = tk.Label(
            self.content_frame,
            text="Gestionnaire d'utilisateurs\n(À implémenter)",
            font=("Arial", 16),
            bg=Colors.WHITE
        )
        label.pack(expand=True)

    def logout(self):
        """Déconnexion"""
        if messagebox.askyesno("Déconnexion", "Êtes-vous sûr de vouloir vous déconnecter ?"):
            self.root.destroy()

    def show(self):
        self.root.mainloop()
