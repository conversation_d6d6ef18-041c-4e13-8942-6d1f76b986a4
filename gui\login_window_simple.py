import tkinter as tk
from tkinter import messagebox
from database import Database
from utils import center_window, Colors, Fonts, Spacing

class LoginWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("📦 Système de Gestion de Stock - Connexion")
        self.root.configure(bg=Colors.BACKGROUND)
        self.root.resizable(False, False)
        
        # Centrer la fenêtre
        center_window(self.root, 450, 500)
        
        self.db = Database()
        self.user = None
        
        self.create_widgets()

    def create_widgets(self):
        # Frame principal
        main_frame = tk.Frame(self.root, bg=Colors.SURFACE, relief=tk.FLAT, bd=1,
                             highlightbackground=Colors.BORDER, highlightthickness=1)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=Spacing.LG, pady=Spacing.LG)
        
        # Contenu
        content_frame = tk.Frame(main_frame, bg=Colors.SURFACE)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=Spacing.XL, pady=Spacing.XL)
        
        # Logo et titre
        self.create_header(content_frame)
        
        # Formulaire
        self.create_form(content_frame)
        
        # Informations
        self.create_info(content_frame)
        
        # Bind Enter key
        self.root.bind('<Return>', lambda event: self.login())
        
        # Focus sur le champ username
        self.username_entry.focus()
    
    def create_header(self, parent):
        """Crée l'en-tête"""
        header_frame = tk.Frame(parent, bg=Colors.SURFACE)
        header_frame.pack(fill=tk.X, pady=(0, Spacing.XL))
        
        # Logo
        logo_frame = tk.Frame(header_frame, bg=Colors.PRIMARY, width=60, height=60)
        logo_frame.pack_propagate(False)
        logo_frame.pack(pady=(0, Spacing.MD))
        
        logo_label = tk.Label(
            logo_frame,
            text="📦",
            font=(Fonts.PRIMARY_FONT, 24),
            bg=Colors.PRIMARY,
            fg=Colors.WHITE
        )
        logo_label.pack(expand=True)
        
        # Titre
        title_label = tk.Label(
            header_frame,
            text="SYSTÈME DE GESTION",
            font=(Fonts.PRIMARY_FONT, Fonts.TITLE_SIZE, "bold"),
            bg=Colors.SURFACE,
            fg=Colors.TEXT_PRIMARY
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            header_frame,
            text="DE STOCK",
            font=(Fonts.PRIMARY_FONT, Fonts.SUBTITLE_SIZE, "bold"),
            bg=Colors.SURFACE,
            fg=Colors.PRIMARY
        )
        subtitle_label.pack()
        
        desc_label = tk.Label(
            header_frame,
            text="Connectez-vous pour accéder à votre espace",
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE),
            bg=Colors.SURFACE,
            fg=Colors.TEXT_SECONDARY
        )
        desc_label.pack(pady=(Spacing.SM, 0))
    
    def create_form(self, parent):
        """Crée le formulaire"""
        form_frame = tk.Frame(parent, bg=Colors.SURFACE)
        form_frame.pack(fill=tk.X, pady=Spacing.LG)
        
        # Nom d'utilisateur
        username_label = tk.Label(
            form_frame,
            text="👤 Nom d'utilisateur",
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE, "bold"),
            bg=Colors.SURFACE,
            fg=Colors.TEXT_PRIMARY
        )
        username_label.pack(anchor=tk.W, pady=(0, Spacing.XS))
        
        self.username_entry = tk.Entry(
            form_frame,
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE),
            bg=Colors.WHITE,
            fg=Colors.TEXT_PRIMARY,
            relief=tk.FLAT,
            bd=5,
            highlightbackground=Colors.BORDER,
            highlightthickness=1
        )
        self.username_entry.pack(fill=tk.X, pady=(0, Spacing.MD))
        
        # Mot de passe
        password_label = tk.Label(
            form_frame,
            text="🔒 Mot de passe",
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE, "bold"),
            bg=Colors.SURFACE,
            fg=Colors.TEXT_PRIMARY
        )
        password_label.pack(anchor=tk.W, pady=(0, Spacing.XS))
        
        self.password_entry = tk.Entry(
            form_frame,
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE),
            bg=Colors.WHITE,
            fg=Colors.TEXT_PRIMARY,
            show="*",
            relief=tk.FLAT,
            bd=5,
            highlightbackground=Colors.BORDER,
            highlightthickness=1
        )
        self.password_entry.pack(fill=tk.X, pady=(0, Spacing.LG))
        
        # Bouton de connexion
        login_btn = tk.Button(
            form_frame,
            text="🔐 SE CONNECTER",
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE, "bold"),
            bg=Colors.PRIMARY,
            fg=Colors.WHITE,
            relief=tk.FLAT,
            bd=0,
            padx=Spacing.LG,
            pady=Spacing.MD,
            command=self.login,
            cursor="hand2"
        )
        login_btn.pack(fill=tk.X, pady=Spacing.MD)
        
        # Effets hover pour le bouton
        def on_enter(e):
            login_btn.configure(bg=Colors.PRIMARY_DARK)
        
        def on_leave(e):
            login_btn.configure(bg=Colors.PRIMARY)
        
        login_btn.bind("<Enter>", on_enter)
        login_btn.bind("<Leave>", on_leave)
    
    def create_info(self, parent):
        """Crée la section d'informations"""
        info_frame = tk.Frame(parent, bg=Colors.SURFACE)
        info_frame.pack(fill=tk.X, pady=Spacing.LG)
        
        # Séparateur
        separator = tk.Frame(info_frame, bg=Colors.BORDER, height=1)
        separator.pack(fill=tk.X, pady=Spacing.MD)
        
        # Carte d'information
        info_card = tk.Frame(
            info_frame,
            bg=Colors.INFO_LIGHT,
            relief=tk.FLAT,
            bd=1,
            highlightbackground=Colors.INFO,
            highlightthickness=1
        )
        info_card.pack(fill=tk.X, pady=Spacing.MD)
        
        info_content = tk.Frame(info_card, bg=Colors.INFO_LIGHT)
        info_content.pack(fill=tk.X, padx=Spacing.MD, pady=Spacing.MD)
        
        info_title = tk.Label(
            info_content,
            text="ℹ️ Compte par défaut",
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE, "bold"),
            bg=Colors.INFO_LIGHT,
            fg=Colors.INFO_DARK
        )
        info_title.pack(anchor=tk.W)
        
        account_details = [
            "👤 Utilisateur: admin",
            "🔑 Mot de passe: admin123",
            "🛡️ Rôle: Administrateur"
        ]
        
        for detail in account_details:
            detail_label = tk.Label(
                info_content,
                text=detail,
                font=(Fonts.PRIMARY_FONT, Fonts.SMALL_SIZE),
                bg=Colors.INFO_LIGHT,
                fg=Colors.INFO_DARK
            )
            detail_label.pack(anchor=tk.W, pady=(2, 0))
    
    def login(self):
        """Fonction de connexion"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        # Validation simple
        if not username:
            messagebox.showerror("❌ Erreur", "Veuillez saisir votre nom d'utilisateur")
            self.username_entry.focus()
            return
            
        if not password:
            messagebox.showerror("❌ Erreur", "Veuillez saisir votre mot de passe")
            self.password_entry.focus()
            return

        # Vérification avec la base de données
        user = self.db.verify_user(username, password)

        if user:
            self.user = user
            self.root.destroy()
        else:
            messagebox.showerror(
                "❌ Erreur de Connexion",
                "Nom d'utilisateur ou mot de passe incorrect.\n\n"
                "Utilisez le compte par défaut:\n"
                "• Utilisateur: admin\n"
                "• Mot de passe: admin123"
            )
            # Effacer le mot de passe et remettre le focus
            self.password_entry.delete(0, tk.END)
            self.username_entry.focus()
    
    def show(self):
        self.root.mainloop()
        return self.user
