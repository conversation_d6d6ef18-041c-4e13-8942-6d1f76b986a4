#!/usr/bin/env python3
"""
Système de Gestion de Stock
Application de gestion de stock avec interface graphique Tkinter

Fonctionnalités:
- Gestion des produits (CRUD)
- Enregistrement des entrées et sorties
- Rapports mensuels et personnalisés
- Système d'authentification avec rôles
- Interface graphique moderne

Auteur: Assistant IA
Date: 2024
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.login_window_simple import LoginWindow
from gui.main_window_modern import MainWindow
from database import Database

def main():
    """Fonction principale de l'application"""
    try:
        # Initialiser la base de données
        db = Database()

        # Afficher la fenêtre de connexion
        login_window = LoginWindow()
        user = login_window.show()

        # Si l'utilisateur s'est connecté avec succès
        if user:
            # Afficher la fenêtre principale
            main_window = MainWindow(user)
            main_window.show()

    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur lors du démarrage de l'application:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
