from datetime import datetime
import tkinter as tk
from tkinter import messagebox, font
import math

def validate_date(date_string):
    """Valide le format de date YYYY-MM-DD"""
    try:
        datetime.strptime(date_string, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def validate_number(value_string):
    """Valide qu'une chaîne est un nombre positif"""
    try:
        value = float(value_string)
        return value >= 0
    except ValueError:
        return False

def show_error(title, message):
    """Affiche un message d'erreur"""
    messagebox.showerror(title, message)

def show_success(title, message):
    """Affiche un message de succès"""
    messagebox.showinfo(title, message)

def show_warning(title, message):
    """Affiche un message d'avertissement"""
    messagebox.showwarning(title, message)

def confirm_action(title, message):
    """Demande confirmation pour une action"""
    return messagebox.askyesno(title, message)

def center_window(window, width, height):
    """Centre une fenêtre sur l'écran"""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()

    x = (screen_width - width) // 2
    y = (screen_height - height) // 2

    window.geometry(f"{width}x{height}+{x}+{y}")

def format_date_for_display(date_string):
    """Formate une date pour l'affichage"""
    try:
        date_obj = datetime.strptime(date_string, '%Y-%m-%d')
        return date_obj.strftime('%d/%m/%Y')
    except ValueError:
        return date_string

def get_current_date():
    """Retourne la date actuelle au format YYYY-MM-DD"""
    return datetime.now().strftime('%Y-%m-%d')

def get_current_datetime():
    """Retourne la date et heure actuelles formatées"""
    return datetime.now().strftime('%d/%m/%Y %H:%M')

class Colors:
    """Palette de couleurs moderne et attractive"""
    # Couleurs principales avec dégradés
    PRIMARY = "#667eea"  # Bleu violet moderne
    PRIMARY_DARK = "#5a67d8"
    PRIMARY_LIGHT = "#a3bffa"

    SECONDARY = "#f093fb"  # Rose moderne
    SECONDARY_DARK = "#ed64a6"
    SECONDARY_LIGHT = "#fbb6ce"

    SUCCESS = "#48bb78"  # Vert moderne
    SUCCESS_DARK = "#38a169"
    SUCCESS_LIGHT = "#9ae6b4"

    WARNING = "#ed8936"  # Orange moderne
    WARNING_DARK = "#dd6b20"
    WARNING_LIGHT = "#fbd38d"

    DANGER = "#f56565"  # Rouge moderne
    DANGER_DARK = "#e53e3e"
    DANGER_LIGHT = "#feb2b2"

    INFO = "#4299e1"  # Bleu info
    INFO_DARK = "#3182ce"
    INFO_LIGHT = "#90cdf4"

    # Couleurs neutres
    DARK = "#2d3748"  # Gris foncé moderne
    DARK_LIGHT = "#4a5568"

    GRAY = "#718096"  # Gris moyen
    GRAY_LIGHT = "#a0aec0"
    GRAY_LIGHTER = "#e2e8f0"

    LIGHT = "#f7fafc"  # Gris très clair
    WHITE = "#ffffff"

    # Couleurs spéciales
    ACCENT = "#9f7aea"  # Violet accent
    ACCENT_LIGHT = "#d6bcfa"

    BACKGROUND = "#f8f9fa"  # Arrière-plan principal
    SURFACE = "#ffffff"  # Surface des cartes

    # Couleurs de texte
    TEXT_PRIMARY = "#2d3748"
    TEXT_SECONDARY = "#718096"
    TEXT_LIGHT = "#a0aec0"

    # Couleurs de bordure
    BORDER = "#e2e8f0"
    BORDER_LIGHT = "#f1f5f9"

    # Couleurs d'ombre (Tkinter ne supporte pas la transparence)
    SHADOW = "#e0e0e0"
    SHADOW_DARK = "#d0d0d0"

class Fonts:
    """Polices et tailles de texte"""
    # Familles de polices
    PRIMARY_FONT = "Segoe UI"
    SECONDARY_FONT = "Arial"
    MONOSPACE_FONT = "Consolas"

    # Tailles
    TITLE_SIZE = 24
    SUBTITLE_SIZE = 18
    HEADING_SIZE = 16
    BODY_SIZE = 11
    SMALL_SIZE = 9
    CAPTION_SIZE = 8

class Spacing:
    """Espacement et dimensions"""
    # Marges et padding
    XS = 4
    SM = 8
    MD = 16
    LG = 24
    XL = 32
    XXL = 48

    # Rayons de bordure
    RADIUS_SM = 4
    RADIUS_MD = 8
    RADIUS_LG = 12
    RADIUS_XL = 16

    # Hauteurs d'éléments
    BUTTON_HEIGHT = 40
    INPUT_HEIGHT = 36
    HEADER_HEIGHT = 60
    SIDEBAR_WIDTH = 250

def create_gradient_frame(parent, color1, color2, width=None, height=None):
    """Crée un frame avec effet de dégradé (simulé)"""
    frame = tk.Frame(parent, bg=color1)
    if width and height:
        frame.configure(width=width, height=height)
    return frame

def create_card_frame(parent, bg_color=None, shadow=True):
    """Crée un frame avec style de carte moderne"""
    if bg_color is None:
        bg_color = Colors.SURFACE

    # Conteneur pour l'effet d'ombre simulé
    if shadow:
        container = tk.Frame(parent, bg=parent.cget('bg') if hasattr(parent, 'cget') else Colors.BACKGROUND)

        # Frame d'ombre (décalé légèrement)
        shadow_frame = tk.Frame(
            container,
            bg=Colors.SHADOW,
            relief=tk.FLAT
        )
        shadow_frame.place(x=3, y=3, relwidth=1, relheight=1)

        # Frame principal de la carte
        card_frame = tk.Frame(
            container,
            bg=bg_color,
            relief=tk.FLAT,
            bd=1,
            highlightbackground=Colors.BORDER,
            highlightthickness=1
        )
        card_frame.place(x=0, y=0, relwidth=1, relheight=1)

        return container
    else:
        # Frame simple sans ombre
        card_frame = tk.Frame(
            parent,
            bg=bg_color,
            relief=tk.FLAT,
            bd=1,
            highlightbackground=Colors.BORDER,
            highlightthickness=1
        )
        return card_frame

def create_modern_button(parent, text, command=None, style="primary", size="medium"):
    """Crée un bouton avec style moderne"""
    # Définir les couleurs selon le style
    style_colors = {
        "primary": (Colors.PRIMARY, Colors.WHITE, Colors.PRIMARY_DARK),
        "secondary": (Colors.SECONDARY, Colors.WHITE, Colors.SECONDARY_DARK),
        "success": (Colors.SUCCESS, Colors.WHITE, Colors.SUCCESS_DARK),
        "warning": (Colors.WARNING, Colors.WHITE, Colors.WARNING_DARK),
        "danger": (Colors.DANGER, Colors.WHITE, Colors.DANGER_DARK),
        "info": (Colors.INFO, Colors.WHITE, Colors.INFO_DARK),
        "light": (Colors.LIGHT, Colors.TEXT_PRIMARY, Colors.GRAY_LIGHTER),
        "dark": (Colors.DARK, Colors.WHITE, Colors.DARK_LIGHT)
    }

    bg_color, fg_color, hover_color = style_colors.get(style, style_colors["primary"])

    # Définir les tailles
    size_config = {
        "small": (Fonts.SMALL_SIZE, Spacing.SM, Spacing.XS),
        "medium": (Fonts.BODY_SIZE, Spacing.MD, Spacing.SM),
        "large": (Fonts.HEADING_SIZE, Spacing.LG, Spacing.MD)
    }

    font_size, padx, pady = size_config.get(size, size_config["medium"])

    button = tk.Button(
        parent,
        text=text,
        command=command,
        bg=bg_color,
        fg=fg_color,
        font=(Fonts.PRIMARY_FONT, font_size, "bold"),
        relief=tk.FLAT,
        bd=0,
        padx=padx,
        pady=pady,
        cursor="hand2"
    )

    # Effets hover
    def on_enter(e):
        button.configure(bg=hover_color)

    def on_leave(e):
        button.configure(bg=bg_color)

    button.bind("<Enter>", on_enter)
    button.bind("<Leave>", on_leave)

    return button

def create_modern_entry(parent, placeholder="", width=None):
    """Crée un champ de saisie avec style moderne"""
    entry_frame = tk.Frame(parent, bg=Colors.WHITE, relief=tk.FLAT, bd=1,
                          highlightbackground=Colors.BORDER, highlightthickness=1)

    entry = tk.Entry(
        entry_frame,
        font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE),
        bg=Colors.WHITE,
        fg=Colors.TEXT_PRIMARY,
        relief=tk.FLAT,
        bd=0
    )

    if width:
        entry.configure(width=width)

    entry.pack(padx=Spacing.SM, pady=Spacing.XS, fill=tk.X, expand=True)

    # Placeholder effect
    if placeholder:
        entry.insert(0, placeholder)
        entry.configure(fg=Colors.TEXT_LIGHT)

        def on_focus_in(event):
            if entry.get() == placeholder:
                entry.delete(0, tk.END)
                entry.configure(fg=Colors.TEXT_PRIMARY)

        def on_focus_out(event):
            if not entry.get():
                entry.insert(0, placeholder)
                entry.configure(fg=Colors.TEXT_LIGHT)

        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)

    # Effets focus
    def on_focus(e):
        entry_frame.configure(highlightbackground=Colors.PRIMARY)

    def on_blur(e):
        entry_frame.configure(highlightbackground=Colors.BORDER)

    entry.bind("<FocusIn>", on_focus)
    entry.bind("<FocusOut>", on_blur)

    return entry_frame, entry

def create_icon_label(parent, text, icon="", icon_color=None, text_color=None):
    """Crée un label avec icône"""
    if icon_color is None:
        icon_color = Colors.PRIMARY
    if text_color is None:
        text_color = Colors.TEXT_PRIMARY

    frame = tk.Frame(parent, bg=parent.cget('bg'))

    if icon:
        icon_label = tk.Label(
            frame,
            text=icon,
            font=(Fonts.PRIMARY_FONT, Fonts.HEADING_SIZE),
            fg=icon_color,
            bg=frame.cget('bg')
        )
        icon_label.pack(side=tk.LEFT, padx=(0, Spacing.XS))

    text_label = tk.Label(
        frame,
        text=text,
        font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE),
        fg=text_color,
        bg=frame.cget('bg')
    )
    text_label.pack(side=tk.LEFT)

    return frame

def apply_modern_style(widget, style_type="default"):
    """Applique un style moderne à un widget existant"""
    if style_type == "title":
        widget.configure(
            font=(Fonts.PRIMARY_FONT, Fonts.TITLE_SIZE, "bold"),
            fg=Colors.TEXT_PRIMARY
        )
    elif style_type == "subtitle":
        widget.configure(
            font=(Fonts.PRIMARY_FONT, Fonts.SUBTITLE_SIZE, "bold"),
            fg=Colors.TEXT_PRIMARY
        )
    elif style_type == "heading":
        widget.configure(
            font=(Fonts.PRIMARY_FONT, Fonts.HEADING_SIZE, "bold"),
            fg=Colors.TEXT_PRIMARY
        )
    elif style_type == "body":
        widget.configure(
            font=(Fonts.PRIMARY_FONT, Fonts.BODY_SIZE),
            fg=Colors.TEXT_PRIMARY
        )
    elif style_type == "caption":
        widget.configure(
            font=(Fonts.PRIMARY_FONT, Fonts.CAPTION_SIZE),
            fg=Colors.TEXT_SECONDARY
        )
