# 🏢 Système de Gestion de Stock

Une application complète de gestion de stock développée en Python avec Tkinter, offrant une interface graphique moderne et intuitive.

## ✨ Fonctionnalités

### 🔐 Système d'authentification
- Connexion sécurisée avec nom d'utilisateur et mot de passe
- Deux rôles : **Administrateur** et **Utilisateur**
- Compte administrateur par défaut : `admin` / `admin123`

### 🗂️ Gestion des produits (Admin uniquement)
- ➕ Ajouter de nouveaux produits
- ✏️ Modifier les informations des produits
- 🗑️ Supprimer des produits
- 🔍 Recherche rapide dans la liste des produits
- Suivi automatique du stock actuel

### ➕ Gestion des entrées
- Enregistrement des entrées de stock
- Saisie de la source et de notes
- Mise à jour automatique du stock
- Historique complet des entrées

### ➖ Gestion des sorties
- Enregistrement des sorties de stock
- Vérification automatique du stock disponible
- Saisie de la destination et du motif
- Mise à jour automatique du stock

### 📊 Rapports et statistiques
- Rapports mensuels automatiques
- Rapports sur période personnalisée
- Statistiques détaillées (entrées/sorties)
- Top 5 des produits les plus actifs
- Export en format CSV

## 🚀 Installation et utilisation

### Prérequis
- Python 3.7 ou supérieur
- Modules Python inclus dans la distribution standard

### Installation
1. Clonez ou téléchargez le projet
2. Naviguez vers le dossier du projet
3. Lancez l'application :

```bash
python main.py
```

### Première utilisation
1. Lancez l'application
2. Connectez-vous avec le compte administrateur :
   - **Nom d'utilisateur :** `admin`
   - **Mot de passe :** `admin123`
3. Commencez par ajouter vos produits dans la section "Produits"
4. Enregistrez vos entrées et sorties de stock

## 📁 Structure du projet

```
stock-management/
├── main.py                 # Point d'entrée de l'application
├── database.py             # Gestion de la base de données SQLite
├── models.py               # Classes de données
├── utils.py                # Fonctions utilitaires
├── requirements.txt        # Dépendances (pour référence)
├── gui/                    # Interface graphique
│   ├── __init__.py
│   ├── login_window.py     # Fenêtre de connexion
│   ├── main_window.py      # Fenêtre principale
│   ├── product_manager.py  # Gestion des produits
│   ├── entry_manager.py    # Gestion des entrées
│   ├── exit_manager.py     # Gestion des sorties
│   └── reports.py          # Rapports et statistiques
└── stock_management.db     # Base de données SQLite (créée automatiquement)
```

## 🎨 Interface utilisateur

### Écran de connexion
- Interface simple et épurée
- Validation des identifiants
- Informations du compte par défaut affichées

### Écran principal
- **Barre supérieure :** Titre, informations utilisateur et date
- **Menu latéral :** Navigation entre les modules
- **Zone de contenu :** Affichage dynamique selon la sélection

### Modules disponibles
1. **🗂️ Produits :** Gestion complète des produits
2. **➕ Entrées :** Enregistrement des entrées de stock
3. **➖ Sorties :** Enregistrement des sorties de stock
4. **📊 Rapports :** Génération de rapports et statistiques

## 🔧 Fonctionnalités techniques

### Base de données
- **SQLite** pour la persistance des données
- Tables : `users`, `produits`, `entrees`, `sorties`
- Relations avec clés étrangères
- Intégrité des données garantie

### Sécurité
- Mots de passe hashés avec SHA-256
- Contrôle d'accès basé sur les rôles
- Validation des entrées utilisateur

### Interface
- **Tkinter** pour l'interface graphique
- Design moderne avec couleurs cohérentes
- Responsive et adaptatif
- Messages d'erreur et de confirmation

## 📊 Types de rapports

### Rapport mensuel
- Sélection du mois et de l'année
- Statistiques automatiques
- Top 5 des produits les plus actifs

### Rapport personnalisé
- Sélection de dates de début et fin
- Données filtrées selon la période
- Export possible en CSV

### Contenu des rapports
- Nombre total d'entrées et sorties
- Détail par produit
- Quantités par transaction
- Informations sur les utilisateurs

## 🛠️ Personnalisation

### Ajout de nouveaux utilisateurs
Les administrateurs peuvent ajouter de nouveaux utilisateurs via la base de données ou en étendant l'interface.

### Modification des couleurs
Les couleurs sont définies dans `utils.py` dans la classe `Colors`.

### Extension des fonctionnalités
L'architecture modulaire permet d'ajouter facilement de nouvelles fonctionnalités.

## 📝 Notes importantes

- La base de données est créée automatiquement au premier lancement
- Les stocks sont mis à jour automatiquement lors des entrées/sorties
- Les rapports peuvent être exportés en CSV pour analyse externe
- L'application vérifie la disponibilité du stock avant les sorties

## 🐛 Dépannage

### Problèmes courants
1. **Erreur de base de données :** Vérifiez les permissions d'écriture dans le dossier
2. **Interface qui ne s'affiche pas :** Vérifiez que Tkinter est installé
3. **Erreur d'importation :** Vérifiez que tous les fichiers sont présents

### Support
Pour toute question ou problème, consultez le code source ou créez une issue.

---

**Développé avec ❤️ en Python**
