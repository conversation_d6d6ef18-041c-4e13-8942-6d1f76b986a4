# 📖 Guide d'Utilisation Rapide

## 🚀 Démarrage

### 1. Lancement de l'application
- **Windows :** Double-cliquez sur `start.bat`
- **Ligne de commande :** `python main.py`

### 2. Première connexion
- **Nom d'utilisateur :** `admin`
- **Mot de passe :** `admin123`

## 🗂️ Gestion des Produits (Admin uniquement)

### Ajouter un produit
1. Cliquez sur "🗂️ Produits" dans le menu
2. Cliquez sur "➕ Nouveau Produit"
3. Remplissez les champs :
   - **Code :** Identifiant unique (ex: ORDI001)
   - **Nom :** Nom du produit (ex: Ordinateur portable)
   - **Catégorie :** Type de produit (ex: Informatique)
   - **Unité :** Unité de mesure (ex: Unité, Kg, Litre)
4. Cliquez sur "Ajouter"

### Modifier un produit
1. Dans la liste des produits, clic droit sur le produit
2. Sélectionnez "✏️ Modifier"
3. Modifiez les informations
4. C<PERSON>z sur "Modifier"

### Supprimer un produit
1. Dans la liste des produits, clic droit sur le produit
2. Sélectionnez "🗑️ Supprimer"
3. Confirmez la suppression

### Rechercher un produit
- Utilisez la barre de recherche en haut de la liste
- Tapez le code, nom ou catégorie du produit

## ➕ Enregistrer une Entrée

### Ajouter une entrée de stock
1. Cliquez sur "➕ Entrées" dans le menu
2. Cliquez sur "➕ Nouvelle Entrée"
3. Remplissez les champs :
   - **Produit :** Sélectionnez dans la liste déroulante
   - **Date :** Format YYYY-MM-DD (ex: 2024-01-15)
   - **Quantité :** Nombre d'unités entrées
   - **Source :** D'où vient le produit (optionnel)
   - **Note :** Commentaire libre (optionnel)
4. Cliquez sur "Enregistrer"

### Filtrer les entrées
1. Utilisez les champs "Du" et "Au" pour définir une période
2. Cliquez sur "🔍 Filtrer"
3. Cliquez sur "🔄 Tout afficher" pour annuler le filtre

## ➖ Enregistrer une Sortie

### Ajouter une sortie de stock
1. Cliquez sur "➖ Sorties" dans le menu
2. Cliquez sur "➖ Nouvelle Sortie"
3. Remplissez les champs :
   - **Produit :** Sélectionnez dans la liste (le stock disponible s'affiche)
   - **Date :** Format YYYY-MM-DD
   - **Quantité :** Nombre d'unités sorties (≤ stock disponible)
   - **Destination :** Où va le produit (optionnel)
   - **Motif :** Raison de la sortie (optionnel)
4. Cliquez sur "Enregistrer"

⚠️ **Important :** Vous ne pouvez pas sortir plus que le stock disponible.

## 📊 Générer des Rapports

### Rapport mensuel
1. Cliquez sur "📊 Rapports" dans le menu
2. Sélectionnez "Mensuel"
3. Choisissez le mois et l'année
4. Cliquez sur "📊 Générer le rapport"

### Rapport personnalisé
1. Sélectionnez "Période personnalisée"
2. Saisissez les dates de début et fin (YYYY-MM-DD)
3. Cliquez sur "📊 Générer le rapport"

### Exporter un rapport
1. Après avoir généré un rapport
2. Cliquez sur "💾 Exporter en CSV"
3. Choisissez l'emplacement de sauvegarde

### Contenu des rapports
- **Résumé :** Statistiques générales et top 5 des produits
- **Entrées :** Détail de toutes les entrées de la période
- **Sorties :** Détail de toutes les sorties de la période

## 🔐 Gestion des Utilisateurs

### Rôles disponibles
- **Admin :** Accès complet (gestion produits + mouvements + rapports)
- **User :** Accès limité (mouvements + rapports uniquement)

### Déconnexion
- Cliquez sur "🔓 Déconnexion" dans le menu
- Confirmez la déconnexion

## 💡 Conseils d'Utilisation

### Bonnes pratiques
1. **Codes produits :** Utilisez un système cohérent (ex: CAT001, CAT002...)
2. **Dates :** Respectez le format YYYY-MM-DD
3. **Quantités :** Utilisez des nombres positifs uniquement
4. **Sauvegarde :** Copiez régulièrement le fichier `stock_management.db`

### Raccourcis
- **Entrée :** Valide les formulaires
- **Échap :** Ferme les boîtes de dialogue
- **Clic droit :** Menu contextuel sur les produits (admin)

### Formats de date acceptés
- ✅ `2024-01-15` (recommandé)
- ❌ `15/01/2024`
- ❌ `15-01-2024`

## 🔧 Résolution de Problèmes

### Problèmes courants

#### "Stock insuffisant"
- Vérifiez le stock disponible du produit
- Réduisez la quantité de sortie
- Ajoutez d'abord une entrée si nécessaire

#### "Ce code produit existe déjà"
- Choisissez un code unique pour chaque produit
- Vérifiez la liste existante avant d'ajouter

#### "Format de date invalide"
- Utilisez le format YYYY-MM-DD
- Vérifiez que la date existe (pas de 31 février)

#### "La quantité doit être un nombre positif"
- Utilisez uniquement des nombres positifs
- Utilisez le point (.) pour les décimales, pas la virgule

### Récupération de données
Si vous avez des problèmes avec la base de données :
1. Fermez l'application
2. Sauvegardez le fichier `stock_management.db`
3. Relancez l'application
4. Si le problème persiste, supprimez le fichier .db (vous perdrez les données)

### Support
- Consultez le fichier `TECHNICAL_DOC.md` pour plus de détails
- Vérifiez que tous les fichiers sont présents
- Assurez-vous d'avoir Python 3.7+ installé

## 📋 Données d'Exemple

### Charger des données de test
Pour tester l'application avec des données réalistes :
```bash
python sample_data.py
```

Cela ajoutera :
- 15 produits d'exemple
- 30 entrées aléatoires
- 20 sorties aléatoires

### Réinitialiser l'application
Pour repartir à zéro :
1. Fermez l'application
2. Supprimez le fichier `stock_management.db`
3. Relancez l'application

---

**Bon usage de votre système de gestion de stock ! 🎉**
