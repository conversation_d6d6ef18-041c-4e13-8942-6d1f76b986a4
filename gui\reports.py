import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import calendar
from utils import Colors, show_error, show_success

class ReportsManager:
    def __init__(self, parent, user, db):
        self.parent = parent
        self.user = user
        self.db = db
        
        self.create_widgets()
    
    def create_widgets(self):
        # Titre
        title_frame = tk.Frame(self.parent, bg=Colors.WHITE)
        title_frame.pack(fill=tk.X, padx=20, pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="📊 RAPPORTS ET STATISTIQUES",
            font=("Arial", 18, "bold"),
            bg=Colors.WHITE,
            fg=Colors.PRIMARY
        )
        title_label.pack(side=tk.LEFT)
        
        # Frame de sélection de période
        period_frame = tk.LabelFrame(
            self.parent,
            text="Sélection de la période",
            font=("Arial", 12, "bold"),
            bg=Colors.WHITE,
            fg=Colors.PRIMARY,
            padx=20,
            pady=15
        )
        period_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Type de rapport
        type_frame = tk.Frame(period_frame, bg=Colors.WHITE)
        type_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(
            type_frame,
            text="Type de rapport:",
            font=("Arial", 10),
            bg=Colors.WHITE
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        self.report_type = tk.StringVar(value="monthly")
        
        monthly_radio = tk.Radiobutton(
            type_frame,
            text="Mensuel",
            variable=self.report_type,
            value="monthly",
            font=("Arial", 10),
            bg=Colors.WHITE,
            command=self.update_period_fields
        )
        monthly_radio.pack(side=tk.LEFT, padx=(0, 15))
        
        custom_radio = tk.Radiobutton(
            type_frame,
            text="Période personnalisée",
            variable=self.report_type,
            value="custom",
            font=("Arial", 10),
            bg=Colors.WHITE,
            command=self.update_period_fields
        )
        custom_radio.pack(side=tk.LEFT)
        
        # Frame pour les champs de période
        self.period_fields_frame = tk.Frame(period_frame, bg=Colors.WHITE)
        self.period_fields_frame.pack(fill=tk.X, pady=10)
        
        self.update_period_fields()
        
        # Boutons d'action
        action_frame = tk.Frame(period_frame, bg=Colors.WHITE)
        action_frame.pack(fill=tk.X, pady=(15, 0))
        
        generate_btn = tk.Button(
            action_frame,
            text="📊 Générer le rapport",
            font=("Arial", 11, "bold"),
            bg=Colors.PRIMARY,
            fg=Colors.WHITE,
            relief=tk.FLAT,
            padx=20,
            pady=10,
            command=self.generate_report
        )
        generate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        export_btn = tk.Button(
            action_frame,
            text="💾 Exporter en CSV",
            font=("Arial", 11),
            bg=Colors.SUCCESS,
            fg=Colors.WHITE,
            relief=tk.FLAT,
            padx=20,
            pady=10,
            command=self.export_report
        )
        export_btn.pack(side=tk.LEFT)
        
        # Zone d'affichage des résultats
        self.create_results_area()
    
    def update_period_fields(self):
        """Met à jour les champs de sélection de période"""
        # Vider le frame
        for widget in self.period_fields_frame.winfo_children():
            widget.destroy()
        
        if self.report_type.get() == "monthly":
            # Sélection mois/année
            tk.Label(
                self.period_fields_frame,
                text="Mois:",
                font=("Arial", 10),
                bg=Colors.WHITE
            ).pack(side=tk.LEFT, padx=(0, 5))
            
            self.month_var = tk.StringVar(value=str(datetime.now().month))
            month_combo = ttk.Combobox(
                self.period_fields_frame,
                textvariable=self.month_var,
                values=[str(i) for i in range(1, 13)],
                state="readonly",
                width=5
            )
            month_combo.pack(side=tk.LEFT, padx=(0, 15))
            
            tk.Label(
                self.period_fields_frame,
                text="Année:",
                font=("Arial", 10),
                bg=Colors.WHITE
            ).pack(side=tk.LEFT, padx=(0, 5))
            
            current_year = datetime.now().year
            self.year_var = tk.StringVar(value=str(current_year))
            year_combo = ttk.Combobox(
                self.period_fields_frame,
                textvariable=self.year_var,
                values=[str(i) for i in range(current_year - 5, current_year + 2)],
                state="readonly",
                width=8
            )
            year_combo.pack(side=tk.LEFT)
            
        else:
            # Période personnalisée
            tk.Label(
                self.period_fields_frame,
                text="Du:",
                font=("Arial", 10),
                bg=Colors.WHITE
            ).pack(side=tk.LEFT, padx=(0, 5))
            
            self.start_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
            start_date_entry = tk.Entry(
                self.period_fields_frame,
                textvariable=self.start_date_var,
                font=("Arial", 10),
                width=12
            )
            start_date_entry.pack(side=tk.LEFT, padx=(0, 15))
            
            tk.Label(
                self.period_fields_frame,
                text="Au:",
                font=("Arial", 10),
                bg=Colors.WHITE
            ).pack(side=tk.LEFT, padx=(0, 5))
            
            self.end_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
            end_date_entry = tk.Entry(
                self.period_fields_frame,
                textvariable=self.end_date_var,
                font=("Arial", 10),
                width=12
            )
            end_date_entry.pack(side=tk.LEFT)
    
    def create_results_area(self):
        """Crée la zone d'affichage des résultats"""
        results_frame = tk.LabelFrame(
            self.parent,
            text="Résultats du rapport",
            font=("Arial", 12, "bold"),
            bg=Colors.WHITE,
            fg=Colors.PRIMARY,
            padx=20,
            pady=15
        )
        results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Notebook pour les onglets
        self.notebook = ttk.Notebook(results_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Onglet Résumé
        self.summary_frame = tk.Frame(self.notebook, bg=Colors.WHITE)
        self.notebook.add(self.summary_frame, text="📈 Résumé")
        
        # Onglet Entrées
        self.entries_frame = tk.Frame(self.notebook, bg=Colors.WHITE)
        self.notebook.add(self.entries_frame, text="➕ Entrées")
        
        # Onglet Sorties
        self.exits_frame = tk.Frame(self.notebook, bg=Colors.WHITE)
        self.notebook.add(self.exits_frame, text="➖ Sorties")
        
        # Message initial
        initial_label = tk.Label(
            self.summary_frame,
            text="Sélectionnez une période et cliquez sur 'Générer le rapport' pour afficher les résultats",
            font=("Arial", 12),
            bg=Colors.WHITE,
            fg=Colors.GRAY
        )
        initial_label.pack(expand=True)
    
    def generate_report(self):
        """Génère le rapport selon la période sélectionnée"""
        try:
            if self.report_type.get() == "monthly":
                month = int(self.month_var.get())
                year = int(self.year_var.get())
                
                # Calculer les dates de début et fin du mois
                start_date = f"{year}-{month:02d}-01"
                if month == 12:
                    end_date = f"{year + 1}-01-01"
                else:
                    end_date = f"{year}-{month + 1:02d}-01"
                
                period_text = f"{calendar.month_name[month]} {year}"
                
            else:
                start_date = self.start_date_var.get()
                end_date = self.end_date_var.get()
                period_text = f"Du {start_date} au {end_date}"
            
            # Récupérer les données
            entries = self.db.get_entries(start_date, end_date)
            exits = self.db.get_exits(start_date, end_date)
            
            # Afficher les résultats
            self.display_summary(entries, exits, period_text)
            self.display_entries(entries)
            self.display_exits(exits)
            
        except Exception as e:
            show_error("Erreur", f"Erreur lors de la génération du rapport: {str(e)}")
    
    def display_summary(self, entries, exits, period_text):
        """Affiche le résumé du rapport"""
        # Vider le frame
        for widget in self.summary_frame.winfo_children():
            widget.destroy()
        
        # Titre
        title_label = tk.Label(
            self.summary_frame,
            text=f"Rapport pour la période: {period_text}",
            font=("Arial", 14, "bold"),
            bg=Colors.WHITE,
            fg=Colors.PRIMARY
        )
        title_label.pack(pady=20)
        
        # Frame pour les statistiques
        stats_frame = tk.Frame(self.summary_frame, bg=Colors.WHITE)
        stats_frame.pack(fill=tk.X, padx=50, pady=20)
        
        # Calculer les totaux
        total_entries = len(entries)
        total_exits = len(exits)
        
        # Calculer les quantités par produit
        entry_quantities = {}
        exit_quantities = {}
        
        for entry in entries:
            product = entry[1]
            quantity = entry[3]
            entry_quantities[product] = entry_quantities.get(product, 0) + quantity
        
        for exit_item in exits:
            product = exit_item[1]
            quantity = exit_item[3]
            exit_quantities[product] = exit_quantities.get(product, 0) + quantity
        
        # Afficher les statistiques
        stats = [
            ("📥 Nombre total d'entrées", total_entries, Colors.SUCCESS),
            ("📤 Nombre total de sorties", total_exits, Colors.DANGER),
            ("🏷️ Produits avec entrées", len(entry_quantities), Colors.PRIMARY),
            ("🏷️ Produits avec sorties", len(exit_quantities), Colors.PRIMARY)
        ]
        
        for i, (label, value, color) in enumerate(stats):
            row = i // 2
            col = i % 2
            
            stat_frame = tk.Frame(stats_frame, bg=color, relief=tk.RAISED, bd=2)
            stat_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
            
            tk.Label(
                stat_frame,
                text=label,
                font=("Arial", 10),
                bg=color,
                fg=Colors.WHITE,
                pady=5
            ).pack()
            
            tk.Label(
                stat_frame,
                text=str(value),
                font=("Arial", 16, "bold"),
                bg=color,
                fg=Colors.WHITE,
                pady=5
            ).pack()
        
        # Configurer les colonnes pour qu'elles s'étendent
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)
        
        # Top 5 des produits avec le plus d'entrées
        if entry_quantities:
            top_entries_frame = tk.LabelFrame(
                self.summary_frame,
                text="Top 5 - Produits avec le plus d'entrées",
                font=("Arial", 11, "bold"),
                bg=Colors.WHITE,
                fg=Colors.SUCCESS
            )
            top_entries_frame.pack(fill=tk.X, padx=50, pady=10)
            
            sorted_entries = sorted(entry_quantities.items(), key=lambda x: x[1], reverse=True)[:5]
            for i, (product, quantity) in enumerate(sorted_entries, 1):
                tk.Label(
                    top_entries_frame,
                    text=f"{i}. {product}: {quantity}",
                    font=("Arial", 10),
                    bg=Colors.WHITE,
                    anchor=tk.W
                ).pack(fill=tk.X, padx=10, pady=2)
        
        # Top 5 des produits avec le plus de sorties
        if exit_quantities:
            top_exits_frame = tk.LabelFrame(
                self.summary_frame,
                text="Top 5 - Produits avec le plus de sorties",
                font=("Arial", 11, "bold"),
                bg=Colors.WHITE,
                fg=Colors.DANGER
            )
            top_exits_frame.pack(fill=tk.X, padx=50, pady=10)
            
            sorted_exits = sorted(exit_quantities.items(), key=lambda x: x[1], reverse=True)[:5]
            for i, (product, quantity) in enumerate(sorted_exits, 1):
                tk.Label(
                    top_exits_frame,
                    text=f"{i}. {product}: {quantity}",
                    font=("Arial", 10),
                    bg=Colors.WHITE,
                    anchor=tk.W
                ).pack(fill=tk.X, padx=10, pady=2)
    
    def display_entries(self, entries):
        """Affiche le détail des entrées"""
        # Vider le frame
        for widget in self.entries_frame.winfo_children():
            widget.destroy()
        
        if not entries:
            tk.Label(
                self.entries_frame,
                text="Aucune entrée pour cette période",
                font=("Arial", 12),
                bg=Colors.WHITE,
                fg=Colors.GRAY
            ).pack(expand=True)
            return
        
        # Créer le tableau
        columns = ("Produit", "Date", "Quantité", "Source", "Note", "Utilisateur")
        tree = ttk.Treeview(self.entries_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor=tk.W if col in ["Produit", "Source", "Note"] else tk.CENTER)
        
        # Ajouter les données
        for entry in entries:
            # Formater la date
            date_formatted = datetime.strptime(entry[2], '%Y-%m-%d').strftime('%d/%m/%Y')
            entry_formatted = list(entry[1:])  # Exclure l'ID
            entry_formatted[1] = date_formatted
            tree.insert("", tk.END, values=entry_formatted)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(self.entries_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=v_scrollbar.set)
        
        # Pack
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def display_exits(self, exits):
        """Affiche le détail des sorties"""
        # Vider le frame
        for widget in self.exits_frame.winfo_children():
            widget.destroy()
        
        if not exits:
            tk.Label(
                self.exits_frame,
                text="Aucune sortie pour cette période",
                font=("Arial", 12),
                bg=Colors.WHITE,
                fg=Colors.GRAY
            ).pack(expand=True)
            return
        
        # Créer le tableau
        columns = ("Produit", "Date", "Quantité", "Destination", "Motif", "Utilisateur")
        tree = ttk.Treeview(self.exits_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor=tk.W if col in ["Produit", "Destination", "Motif"] else tk.CENTER)
        
        # Ajouter les données
        for exit_item in exits:
            # Formater la date
            date_formatted = datetime.strptime(exit_item[2], '%Y-%m-%d').strftime('%d/%m/%Y')
            exit_formatted = list(exit_item[1:])  # Exclure l'ID
            exit_formatted[1] = date_formatted
            tree.insert("", tk.END, values=exit_formatted)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(self.exits_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=v_scrollbar.set)
        
        # Pack
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def export_report(self):
        """Exporte le rapport en CSV"""
        try:
            # Demander le fichier de destination
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Exporter le rapport"
            )
            
            if not filename:
                return
            
            # Récupérer les données selon la période
            if self.report_type.get() == "monthly":
                month = int(self.month_var.get())
                year = int(self.year_var.get())
                start_date = f"{year}-{month:02d}-01"
                if month == 12:
                    end_date = f"{year + 1}-01-01"
                else:
                    end_date = f"{year}-{month + 1:02d}-01"
            else:
                start_date = self.start_date_var.get()
                end_date = self.end_date_var.get()
            
            entries = self.db.get_entries(start_date, end_date)
            exits = self.db.get_exits(start_date, end_date)
            
            # Écrire le fichier CSV
            with open(filename, 'w', encoding='utf-8-sig', newline='') as f:
                f.write("Type,Produit,Date,Quantité,Source/Destination,Note/Motif,Utilisateur\n")
                
                # Écrire les entrées
                for entry in entries:
                    date_formatted = datetime.strptime(entry[2], '%Y-%m-%d').strftime('%d/%m/%Y')
                    f.write(f"Entrée,{entry[1]},{date_formatted},{entry[3]},{entry[4] or ''},{entry[5] or ''},{entry[6] or ''}\n")
                
                # Écrire les sorties
                for exit_item in exits:
                    date_formatted = datetime.strptime(exit_item[2], '%Y-%m-%d').strftime('%d/%m/%Y')
                    f.write(f"Sortie,{exit_item[1]},{date_formatted},{exit_item[3]},{exit_item[4] or ''},{exit_item[5] or ''},{exit_item[6] or ''}\n")
            
            show_success("Succès", f"Rapport exporté avec succès vers:\n{filename}")
            
        except Exception as e:
            show_error("Erreur", f"Erreur lors de l'exportation: {str(e)}")
